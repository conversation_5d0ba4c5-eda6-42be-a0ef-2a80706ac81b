{"info": {"name": "Brand Monitoring API", "description": "# Brand Monitoring System API\n\nA comprehensive API for brand protection and monitoring against fraudulent activities including domain squatting, social media scams, and malicious advertisements.\n\n## Features\n- **Multi-level Permission System**: System Admin, Admin, Editor, Viewer roles\n- **Google Ads Integration**: Monitor and report fraudulent advertisements\n- **Domain Monitoring**: Track suspicious domains and typosquatting\n- **Threat Detection**: Automated threat identification and alerting\n- **Brand Management**: Complete brand portfolio management\n\n## Authentication\nAll endpoints require authentication. Include the user token in the Authorization header:\n```\nAuthorization: Bearer <your-token>\n```\n\n## Permission Levels\n- **System Admin**: Full access including user management and revenue reports\n- **Admin**: Brand and threat management, no user management\n- **Editor**: Edit brands and threats, create reports\n- **Viewer**: Read-only access to all data\n\n## Base URL\n```\nhttp://localhost:3000\n```", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "brand-monitoring-api", "version": {"major": 1, "minor": 0, "patch": 0}}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "auth_token", "value": "your-jwt-token-here", "type": "string"}, {"key": "brand_id", "value": "sample-brand-id", "type": "string"}, {"key": "user_id", "value": "sample-user-id", "type": "string"}], "item": [{"name": "🔐 Authentication & RBAC", "description": "User authentication and role-based access control endpoints", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/rbac/users", "host": ["{{base_url}}"], "path": ["api", "rbac", "users"]}, "description": "**Permission Required**: `users:read` (Admin or System Admin)\n\nRetrieve all active users in the system with their roles and permissions.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": \"user-id\",\n      \"username\": \"john.doe\",\n      \"email\": \"<EMAIL>\",\n      \"name\": \"<PERSON>\",\n      \"status\": \"active\",\n      \"defaultRole\": \"admin\",\n      \"roles\": [\n        {\n          \"id\": \"role-id\",\n          \"name\": \"Brand Administrator\",\n          \"description\": \"Manage brands and monitor threats\"\n        }\n      ],\n      \"createdAt\": \"2024-01-01T00:00:00.000Z\"\n    }\n  ]\n}\n```"}, "response": []}, {"name": "Assign Role to User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/roles/{{role_id}}", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "roles", "{{role_id}}"]}, "description": "**Permission Required**: `users:manage` (System Admin only)\n\nAssign a role to a user. Users can have multiple roles for flexible permission management.\n\n### Path Parameters\n- `user_id`: The ID of the user\n- `role_id`: The ID of the role to assign\n\n### Response\n```json\n{\n  \"success\": true,\n  \"message\": \"Role assigned successfully\"\n}\n```"}, "response": []}, {"name": "Get User Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/rbac/users/{{user_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "rbac", "users", "{{user_id}}", "permissions"]}, "description": "**Permission Required**: `users:read` or own user data\n\nGet all permissions for a specific user, including both role-based and default permissions.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"userId\": \"user-id\",\n    \"permissions\": [\n      \"brands:read\",\n      \"brands:create\",\n      \"threats:read\",\n      \"threats:update\",\n      \"reports:read\"\n    ]\n  }\n}\n```"}, "response": []}, {"name": "Check Permission", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/rbac/check-permission/brands/create", "host": ["{{base_url}}"], "path": ["api", "rbac", "check-permission", "brands", "create"]}, "description": "Check if the current user has a specific permission.\n\n### Path Parameters\n- `resource`: The resource (e.g., brands, threats, users)\n- `action`: The action (e.g., create, read, update, delete, manage)\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"userId\": \"user-id\",\n    \"resource\": \"brands\",\n    \"action\": \"create\",\n    \"hasPermission\": true\n  }\n}\n```"}, "response": []}, {"name": "Get All Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/rbac/roles", "host": ["{{base_url}}"], "path": ["api", "rbac", "roles"]}, "description": "**Permission Required**: `users:read`\n\nRetrieve all available roles in the system with their permissions.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": \"role-id\",\n      \"name\": \"System Administrator\",\n      \"description\": \"Full system access with all permissions\",\n      \"isSystemRole\": true,\n      \"permissionCount\": 25,\n      \"permissions\": [\n        {\n          \"id\": \"perm-id\",\n          \"name\": \"Create Brands\",\n          \"resource\": \"brands\",\n          \"action\": \"create\"\n        }\n      ]\n    }\n  ]\n}\n```"}, "response": []}]}, {"name": "🏢 Brand Management", "description": "Manage brand portfolios and monitoring settings", "item": [{"name": "Get All Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/brands", "host": ["{{base_url}}"], "path": ["api", "brands"]}, "description": "**Permission Required**: `brands:read`\n\nRetrieve all brands in the system with their monitoring status and key metrics.\n\n### Query Parameters\n- `status`: Filter by brand status (active, inactive)\n- `page`: Page number for pagination\n- `limit`: Number of results per page\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": \"brand-id\",\n      \"name\": \"MyCompany\",\n      \"description\": \"Leading technology company\",\n      \"primaryDomain\": \"mycompany.com\",\n      \"keywords\": [\"mycompany\", \"technology\", \"innovation\"],\n      \"status\": \"active\",\n      \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n      \"threatCount\": 5,\n      \"domainCount\": 12\n    }\n  ],\n  \"total\": 1,\n  \"page\": 1,\n  \"limit\": 10\n}\n```"}, "response": []}, {"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"TechCorp\",\n  \"description\": \"Innovative technology solutions\",\n  \"primaryDomain\": \"techcorp.com\",\n  \"keywords\": [\"techcorp\", \"technology\", \"innovation\", \"software\"],\n  \"industry\": \"Technology\",\n  \"monitoringEnabled\": true\n}"}, "url": {"raw": "{{base_url}}/api/brands", "host": ["{{base_url}}"], "path": ["api", "brands"]}, "description": "**Permission Required**: `brands:create`\n\nCreate a new brand for monitoring. This will automatically start domain and threat monitoring.\n\n### Request Body\n```json\n{\n  \"name\": \"Brand Name\",\n  \"description\": \"Brand description\",\n  \"primaryDomain\": \"brand.com\",\n  \"keywords\": [\"keyword1\", \"keyword2\"],\n  \"industry\": \"Industry name\",\n  \"monitoringEnabled\": true\n}\n```\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"new-brand-id\",\n    \"name\": \"TechCorp\",\n    \"status\": \"active\",\n    \"createdAt\": \"2024-01-01T00:00:00.000Z\"\n  },\n  \"message\": \"Brand created successfully\"\n}\n```"}, "response": []}, {"name": "Get Brand Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/brands/{{brand_id}}", "host": ["{{base_url}}"], "path": ["api", "brands", "{{brand_id}}"]}, "description": "**Permission Required**: `brands:read`\n\nGet detailed information about a specific brand including recent threats and monitoring statistics.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"brand-id\",\n    \"name\": \"MyCompany\",\n    \"description\": \"Leading technology company\",\n    \"primaryDomain\": \"mycompany.com\",\n    \"keywords\": [\"mycompany\", \"technology\"],\n    \"status\": \"active\",\n    \"statistics\": {\n      \"totalThreats\": 15,\n      \"activeThreats\": 3,\n      \"domainsMonitored\": 25,\n      \"lastScanDate\": \"2024-01-01T12:00:00.000Z\"\n    },\n    \"recentThreats\": [\n      {\n        \"id\": \"threat-id\",\n        \"type\": \"domain_squatting\",\n        \"severity\": \"high\",\n        \"detectedAt\": \"2024-01-01T10:00:00.000Z\"\n      }\n    ]\n  }\n}\n```"}, "response": []}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"TechCorp Updated\",\n  \"description\": \"Updated description\",\n  \"keywords\": [\"techcorp\", \"technology\", \"innovation\", \"software\", \"AI\"],\n  \"monitoringEnabled\": true\n}"}, "url": {"raw": "{{base_url}}/api/brands/{{brand_id}}", "host": ["{{base_url}}"], "path": ["api", "brands", "{{brand_id}}"]}, "description": "**Permission Required**: `brands:update`\n\nUpdate brand information and monitoring settings.\n\n### Request Body\nInclude only the fields you want to update:\n```json\n{\n  \"name\": \"Updated Brand Name\",\n  \"description\": \"Updated description\",\n  \"keywords\": [\"new\", \"keywords\"],\n  \"monitoringEnabled\": false\n}\n```"}, "response": []}]}, {"name": "🔍 Google Ads Monitoring", "description": "Monitor and manage fraudulent advertisements across Google Ads platform", "item": [{"name": "Search Brand Ads", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/google-ads/brands/{{brand_id}}/ads", "host": ["{{base_url}}"], "path": ["api", "google-ads", "brands", "{{brand_id}}", "ads"]}, "description": "**Permission Required**: `threats:read`\n\nSearch for advertisements related to a specific brand across Google Ads platform. This endpoint identifies potentially fraudulent ads using brand names and keywords.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"adId\": \"ad_123456789\",\n      \"headline\": \"MyCompany - Best Deals Online\",\n      \"description\": \"Shop MyCompany with huge discounts. Free shipping worldwide.\",\n      \"displayUrl\": \"www.mycompany-deals.com\",\n      \"finalUrl\": \"https://mycompany-deals.com\",\n      \"keywords\": [\"mycompany\", \"discount\", \"sale\"],\n      \"impressions\": 5420,\n      \"clicks\": 234,\n      \"cost\": 567.89,\n      \"status\": \"active\",\n      \"createdAt\": \"2024-01-01T00:00:00.000Z\",\n      \"brandSimilarity\": 0.85,\n      \"riskScore\": 0.92\n    }\n  ],\n  \"total\": 1\n}\n```\n\n### Risk Score Explanation\n- **0.0 - 0.3**: Low risk\n- **0.4 - 0.6**: Medium risk  \n- **0.7 - 0.8**: High risk\n- **0.9 - 1.0**: Critical risk"}, "response": []}, {"name": "Monitor All Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/google-ads/monitor/all", "host": ["{{base_url}}"], "path": ["api", "google-ads", "monitor", "all"]}, "description": "**Permission Required**: `brands:manage` (Admin or System Admin only)\n\nRun comprehensive monitoring across all brands to detect fraudulent advertisements. This is typically used for scheduled monitoring tasks.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"brandId\": \"brand-id\",\n      \"brandName\": \"MyCompany\",\n      \"totalAdsFound\": 15,\n      \"suspiciousAds\": [\n        {\n          \"adId\": \"ad_123\",\n          \"headline\": \"Fake MyCompany Store\",\n          \"riskScore\": 0.95\n        }\n      ],\n      \"highRiskAds\": [\n        {\n          \"adId\": \"ad_456\",\n          \"headline\": \"MyCompany Replica\",\n          \"riskScore\": 0.98\n        }\n      ],\n      \"totalImpressions\": 25000,\n      \"totalClicks\": 1200,\n      \"estimatedLoss\": 5600.00,\n      \"reportDate\": \"2024-01-01T12:00:00.000Z\"\n    }\n  ],\n  \"summary\": {\n    \"totalBrands\": 5,\n    \"totalSuspiciousAds\": 12,\n    \"totalHighRiskAds\": 3,\n    \"totalEstimatedLoss\": 15600.00\n  }\n}\n```"}, "response": []}, {"name": "Report Infringing Ad", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Trademark infringement - Using our brand name without authorization to sell counterfeit products\"\n}"}, "url": {"raw": "{{base_url}}/api/google-ads/ads/{{ad_id}}/report", "host": ["{{base_url}}"], "path": ["api", "google-ads", "ads", "{{ad_id}}", "report"]}, "description": "**Permission Required**: `threats:update`\n\nReport a fraudulent advertisement to Google for takedown. This creates a formal complaint and tracks the reporting status.\n\n### Request Body\n```json\n{\n  \"reason\": \"Detailed reason for reporting the ad\"\n}\n```\n\n### Common Reporting Reasons\n- Trademark infringement\n- Counterfeit products\n- Misleading advertising\n- Phishing/scam content\n- Unauthorized use of brand assets\n\n### Response\n```json\n{\n  \"success\": true,\n  \"message\": \"Ad reported successfully\"\n}\n```"}, "response": []}, {"name": "Get Ad Performance Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/google-ads/brands/{{brand_id}}/metrics?startDate=2024-01-01&endDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "google-ads", "brands", "{{brand_id}}", "metrics"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}]}, "description": "**Permission Required**: `reports:read`\n\nGet detailed performance metrics for ad monitoring of a specific brand over a date range.\n\n### Query Parameters\n- `startDate`: Start date (YYYY-MM-DD format)\n- `endDate`: End date (YYYY-MM-DD format)\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"brandId\": \"brand-id\",\n    \"dateRange\": {\n      \"start\": \"2024-01-01T00:00:00.000Z\",\n      \"end\": \"2024-01-31T23:59:59.000Z\"\n    },\n    \"metrics\": {\n      \"totalImpressions\": 45000,\n      \"totalClicks\": 2300,\n      \"averageCpc\": 2.45,\n      \"conversionRate\": 0.034,\n      \"suspiciousAdImpressions\": 850,\n      \"estimatedRevenueLoss\": 8500.00\n    }\n  }\n}\n```"}, "response": []}, {"name": "Get Monitoring Dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/google-ads/dashboard", "host": ["{{base_url}}"], "path": ["api", "google-ads", "dashboard"]}, "description": "**Permission Required**: `reports:read`\n\nGet a comprehensive dashboard view of Google Ads monitoring across all brands.\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"overview\": {\n      \"totalBrandsMonitored\": 8,\n      \"totalAdsFound\": 156,\n      \"suspiciousAdsCount\": 23,\n      \"highRiskAdsCount\": 7,\n      \"totalEstimatedLoss\": 25600.00\n    },\n    \"topRiskyBrands\": [\n      {\n        \"brandName\": \"TechCorp\",\n        \"highRiskAdsCount\": 4,\n        \"estimatedLoss\": 12000.00\n      }\n    ],\n    \"recentHighRiskAds\": [\n      {\n        \"adId\": \"ad_789\",\n        \"brandName\": \"TechCorp\",\n        \"headline\": \"Fake TechCorp Products\",\n        \"riskScore\": 0.96,\n        \"impressions\": 5600\n      }\n    ]\n  }\n}\n```"}, "response": []}]}, {"name": "⚠️ Threat Detection", "description": "Monitor and manage security threats across all platforms", "item": [{"name": "Get All Threats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/threats?status=active&severity=high&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "threats"], "query": [{"key": "status", "value": "active"}, {"key": "severity", "value": "high"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "**Permission Required**: `threats:read`\n\nRetrieve all detected threats with filtering and pagination options.\n\n### Query Parameters\n- `status`: Filter by status (active, resolved, investigating)\n- `severity`: Filter by severity (low, medium, high, critical)\n- `threatType`: Filter by type (domain_squatting, phishing, fraudulent_advertising)\n- `brandId`: Filter by specific brand\n- `page`: Page number\n- `limit`: Results per page\n\n### Response\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": \"threat-id\",\n      \"brandId\": \"brand-id\",\n      \"threatType\": \"domain_squatting\",\n      \"severity\": \"high\",\n      \"status\": \"active\",\n      \"sourceUrl\": \"https://mycompany-fake.com\",\n      \"description\": \"Suspicious domain detected with high similarity to brand domain\",\n      \"firstDetected\": \"2024-01-01T10:00:00.000Z\",\n      \"lastUpdated\": \"2024-01-01T15:30:00.000Z\",\n      \"metadata\": {\n        \"similarityScore\": 0.92,\n        \"detectionMethod\": \"automated_scan\"\n      }\n    }\n  ],\n  \"total\": 25,\n  \"page\": 1,\n  \"limit\": 10\n}\n```"}, "response": []}, {"name": "Update Threat Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"investigating\",\n  \"notes\": \"Contacted legal team for trademark infringement case\",\n  \"assignedTo\": \"user-id\",\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/api/threats/{{threat_id}}", "host": ["{{base_url}}"], "path": ["api", "threats", "{{threat_id}}"]}, "description": "**Permission Required**: `threats:update`\n\nUpdate threat status and add investigation notes.\n\n### Request Body\n```json\n{\n  \"status\": \"investigating|resolved|escalated\",\n  \"notes\": \"Investigation notes\",\n  \"assignedTo\": \"user-id\",\n  \"priority\": \"low|medium|high|critical\"\n}\n```\n\n### Response\n```json\n{\n  \"success\": true,\n  \"message\": \"Threat updated successfully\"\n}\n```"}, "response": []}]}]}