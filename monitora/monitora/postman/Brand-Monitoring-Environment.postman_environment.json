{"id": "brand-monitoring-env", "name": "Brand Monitoring - Development", "values": [{"key": "base_url", "value": "http://localhost:3000", "description": "Base URL for the Brand Monitoring API", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "description": "JWT authentication token - Set this after login", "type": "secret", "enabled": true}, {"key": "brand_id", "value": "sample-brand-id-123", "description": "Sample brand ID for testing", "type": "default", "enabled": true}, {"key": "user_id", "value": "sample-user-id-456", "description": "Sample user ID for testing", "type": "default", "enabled": true}, {"key": "role_id", "value": "sample-role-id-789", "description": "Sample role ID for testing", "type": "default", "enabled": true}, {"key": "threat_id", "value": "sample-threat-id-101", "description": "Sample threat ID for testing", "type": "default", "enabled": true}, {"key": "ad_id", "value": "ad_sample123456", "description": "Sample Google Ad ID for testing", "type": "default", "enabled": true}, {"key": "alert_id", "value": "sample-alert-id-202", "description": "Sample alert ID for testing", "type": "default", "enabled": true}, {"key": "domain_id", "value": "sample-domain-id-303", "description": "Sample domain ID for testing", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}