# Brand Monitoring API - Postman Collection

This directory contains a comprehensive Postman collection and environment for testing the Brand Monitoring System API.

## 📁 Files Included

- **`Brand-Monitoring-API.postman_collection.json`** - Complete API collection with detailed documentation
- **`Brand-Monitoring-Environment.postman_environment.json`** - Environment variables for development testing
- **`README.md`** - This documentation file

## 🚀 Quick Start

### 1. Import into Postman

1. Open Postman
2. Click **Import** button
3. Select **Upload Files**
4. Import both the collection and environment files

### 2. Set Up Environment

1. Select the **Brand Monitoring - Development** environment
2. Update the `auth_token` variable with your JWT token
3. Modify `base_url` if your API runs on a different port/host

### 3. Authentication

Before making API calls, you need to authenticate:

1. Set your JWT token in the environment variable `auth_token`
2. The collection is configured to automatically use this token in the Authorization header

## 📚 API Documentation

### 🔐 Authentication & RBAC

The API uses JWT-based authentication with role-based access control (RBAC):

- **System Admin**: Full access including user management and revenue reports
- **Admin**: Brand and threat management, no user management  
- **Editor**: Edit brands and threats, create reports
- **Viewer**: Read-only access to all data

### 📋 Collection Structure

#### 1. **🔐 Authentication & RBAC**
- Get all users
- Assign/remove roles from users
- Check user permissions
- Manage roles and permissions

#### 2. **🏢 Brand Management**
- CRUD operations for brands
- Brand monitoring settings
- Brand statistics and metrics

#### 3. **🔍 Google Ads Monitoring**
- Search for brand-related ads
- Monitor all brands for fraudulent ads
- Report infringing advertisements
- Get performance metrics and dashboard

#### 4. **⚠️ Threat Detection**
- View and filter threats
- Update threat status and notes
- Assign threats to team members

## 🔧 Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API base URL | `http://localhost:3000` |
| `auth_token` | JWT authentication token | `eyJhbGciOiJIUzI1NiIs...` |
| `brand_id` | Sample brand ID for testing | `sample-brand-id-123` |
| `user_id` | Sample user ID for testing | `sample-user-id-456` |
| `role_id` | Sample role ID for testing | `sample-role-id-789` |
| `threat_id` | Sample threat ID for testing | `sample-threat-id-101` |
| `ad_id` | Sample Google Ad ID | `ad_sample123456` |
| `alert_id` | Sample alert ID for testing | `sample-alert-id-202` |

## 📖 Usage Examples

### Creating a New Brand

```http
POST {{base_url}}/api/brands
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "TechCorp",
  "description": "Innovative technology solutions",
  "primaryDomain": "techcorp.com",
  "keywords": ["techcorp", "technology", "innovation"],
  "industry": "Technology",
  "monitoringEnabled": true
}
```

### Searching for Brand Ads

```http
GET {{base_url}}/api/google-ads/brands/{{brand_id}}/ads
Authorization: Bearer {{auth_token}}
```

### Reporting an Infringing Ad

```http
POST {{base_url}}/api/google-ads/ads/{{ad_id}}/report
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "reason": "Trademark infringement - Using our brand name without authorization"
}
```

## 🔍 Testing Scenarios

### 1. **System Administrator Workflow**
1. Get all users → `GET /api/rbac/users`
2. Create new brand → `POST /api/brands`
3. Monitor all brands → `GET /api/google-ads/monitor/all`
4. View dashboard → `GET /api/google-ads/dashboard`

### 2. **Brand Manager Workflow**
1. Get brand details → `GET /api/brands/{{brand_id}}`
2. Search brand ads → `GET /api/google-ads/brands/{{brand_id}}/ads`
3. Report suspicious ad → `POST /api/google-ads/ads/{{ad_id}}/report`
4. Update threat status → `PUT /api/threats/{{threat_id}}`

### 3. **Viewer Workflow**
1. Search across system → `GET /api/search?query=mycompany`
2. View threats → `GET /api/threats?status=active`
3. Check alerts → `GET /api/alerts?priority=high`

## 🛡️ Permission Testing

Test different permission levels by:

1. Creating users with different roles
2. Testing access to various endpoints
3. Verifying proper 403 Forbidden responses for unauthorized access

### Permission Matrix

| Endpoint | System Admin | Admin | Editor | Viewer |
|----------|--------------|-------|--------|--------|
| `GET /api/brands` | ✅ | ✅ | ✅ | ✅ |
| `POST /api/brands` | ✅ | ✅ | ❌ | ❌ |
| `DELETE /api/brands/:id` | ✅ | ✅ | ❌ | ❌ |
| `GET /api/rbac/users` | ✅ | ❌ | ❌ | ❌ |
| `POST /api/rbac/users/:id/roles/:roleId` | ✅ | ❌ | ❌ | ❌ |
| `GET /api/google-ads/monitor/all` | ✅ | ✅ | ❌ | ❌ |

## 🐛 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if `auth_token` is set correctly
   - Verify token hasn't expired

2. **403 Forbidden**
   - User doesn't have required permissions
   - Check user's role assignments

3. **404 Not Found**
   - Verify the endpoint URL is correct
   - Check if the API server is running

4. **500 Internal Server Error**
   - Check API server logs
   - Verify database connection

### Debug Tips

1. Use Postman Console to view request/response details
2. Check the `Tests` tab for automated response validation
3. Monitor API server logs for detailed error information

## 🔄 Updates and Maintenance

When updating the API:

1. Update the collection with new endpoints
2. Add new environment variables as needed
3. Update this README with new features
4. Test all existing requests to ensure compatibility

## 📞 Support

For issues with the API or Postman collection:

1. Check the API server logs
2. Verify environment variables are set correctly
3. Test with different user roles to isolate permission issues
4. Review the API documentation for endpoint requirements
