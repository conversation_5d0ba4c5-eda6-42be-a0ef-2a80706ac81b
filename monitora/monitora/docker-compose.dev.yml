version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: monitora-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: monitora_db
      POSTGRES_USER: monitora_user
      POSTGRES_PASSWORD: monitora_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - monitora-dev-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: monitora-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass monitora_redis_password
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - monitora-dev-network

  # Development Application (with hot reload)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: monitora-app-dev
    restart: unless-stopped
    environment:
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: monitora_user
      DB_PASSWORD: monitora_password
      DB_DATABASE: monitora_db
      
      # Application
      NODE_ENV: development
      PORT: 3000
      JWT_SECRET: dev_jwt_secret_key
      FRONTEND_URL: http://localhost:3000
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: monitora_redis_password
      
      # Development settings
      LOG_LEVEL: debug
      MOCK_RECORD_MODE: false
      
      # Puppeteer
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: true
      PUPPETEER_EXECUTABLE_PATH: /usr/bin/google-chrome-stable
    
    volumes:
      # Mount source code for hot reload
      - .:/usr/src/app
      - /usr/src/app/node_modules
      - dev_screenshots:/usr/src/app/screenshots
      - dev_reports:/usr/src/app/reports
      - dev_logs:/usr/src/app/logs
    
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    
    networks:
      - monitora-dev-network
    
    depends_on:
      - postgres
      - redis
    
    command: npm run dev

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: monitora-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - monitora-dev-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: monitora-pgadmin-dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - monitora-dev-network
    depends_on:
      - postgres

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: monitora-redis-commander-dev
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:monitora_redis_password
    ports:
      - "8081:8081"
    networks:
      - monitora-dev-network
    depends_on:
      - redis

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local
  dev_screenshots:
    driver: local
  dev_reports:
    driver: local
  dev_logs:
    driver: local

networks:
  monitora-dev-network:
    driver: bridge
