# Docker Environment Configuration for Monitora Brand Protection

# Email Configuration (Required for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# SMS Configuration (Optional - for critical alerts)
SMS_API_KEY=your_twilio_api_key
SMS_API_URL=https://api.twilio.com/2010-04-01
SMS_FROM=+1234567890

# API Keys for External Services

# VirusTotal API (Recommended for malware scanning)
VIRUSTOTAL_API_KEY=your_virustotal_api_key

# Meta (Facebook/Instagram) API
META_ACCESS_TOKEN=your_meta_access_token
META_APP_ID=your_meta_app_id
META_APP_SECRET=your_meta_app_secret

# Twitter API v2
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret

# Google APIs
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_custom_search_engine_id
YOUTUBE_API_KEY=your_youtube_api_key

# Optional: External Threat Intelligence APIs
ALIENVAULT_API_KEY=your_alienvault_api_key
THREATCROWD_API_KEY=your_threatcrowd_api_key
URLVOID_API_KEY=your_urlvoid_api_key

# Webhook Configuration (Optional)
WEBHOOK_SECRET=your_webhook_secret
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url
