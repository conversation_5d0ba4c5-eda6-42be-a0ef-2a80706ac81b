{"name": "monitora-brand-protection", "version": "1.0.0", "description": "Brand monitoring and anti-scam protection system", "scripts": {"build": "tsc", "start": "npx ts-node -r tsconfig-paths/register src/app/server.ts", "dev": "npx ts-node --inspect=0.0.0.0:9229 -r tsconfig-paths/register src/app/server.ts", "start:mock": "MOCK_RECORD_MODE=true npm start", "generate-mocks": "npx ts-node -r tsconfig-paths/register src/core/mock/generate-mock-classes.ts", "test": "jest", "monitor": "npx ts-node -r tsconfig-paths/register src/app/jobs/monitoring.job.ts", "docker:build": "docker build -t monitora-app .", "docker:dev": "docker compose -f docker-compose.dev.yml up --build", "docker:prod": "docker compose up --build -d", "docker:stop": "docker compose down", "docker:logs": "docker compose logs -f app"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/string-similarity": "^4.0.2", "axios": "^0.27.2", "bcrypt": "^6.0.0", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cors": "^2.8.5", "dns": "^0.2.2", "dotenv": "^16.4.7", "express": "^4.18.2", "glob": "^10.4.5", "inversify": "^6.0.1", "jsonwebtoken": "^8.5.1", "node-cron": "^3.0.3", "node-mocks-http": "^1.16.2", "nodemailer": "^6.9.7", "pg": "^8.14.1", "puppeteer": "^21.5.2", "reflect-metadata": "^0.1.13", "similarity": "^1.2.1", "socket.io": "^4.7.4", "string-similarity": "^4.0.4", "typeorm": "^0.3.21", "url-parse": "^1.5.10", "whois": "^2.13.5"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/cors": "^2.8.17", "@types/express": "^4.17.13", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.5.8", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/similarity": "^1.2.3", "@types/url-parse": "^1.4.11", "jest": "^28.1.1", "jest-express": "^1.12.0", "supertest": "^7.0.0", "ts-jest": "^28.0.1", "ts-morph": "^15.1.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^4.7.4"}}