# 🚀 Setup Monitora Without Docker

If you prefer not to use Docker or don't have it installed, you can run Monitora directly on your system.

## 📋 **Prerequisites**

- Node.js 18+ and npm
- PostgreSQL 12+
- Redis 6+ (optional, for caching)
- Chrome/Chromium (for screenshots)

## 🔧 **Installation Steps**

### **1. Install System Dependencies**

#### **macOS**
```bash
# Install Node.js
brew install node

# Install PostgreSQL
brew install postgresql
brew services start postgresql

# Install Redis (optional)
brew install redis
brew services start redis

# Chrome is usually already installed, or:
brew install --cask google-chrome
```

#### **Ubuntu/Debian**
```bash
# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Install Redis (optional)
sudo apt-get install redis-server
sudo systemctl start redis
sudo systemctl enable redis

# Install Chrome for screenshots
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt-get update
sudo apt-get install google-chrome-stable
```

#### **Windows**
```bash
# Install Node.js from: https://nodejs.org/
# Install PostgreSQL from: https://www.postgresql.org/download/windows/
# Install Redis from: https://redis.io/download
# Install Chrome from: https://www.google.com/chrome/
```

### **2. Database Setup**

```bash
# Create database user and database
sudo -u postgres psql

# In PostgreSQL shell:
CREATE USER monitora_user WITH PASSWORD 'monitora_password';
CREATE DATABASE monitora_db OWNER monitora_user;
GRANT ALL PRIVILEGES ON DATABASE monitora_db TO monitora_user;
\q
```

### **3. Application Setup**

```bash
# Clone and navigate to project
cd monitora/monitora

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### **4. Environment Configuration**

Edit `.env` file:
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=monitora_user
DB_PASSWORD=monitora_password
DB_DATABASE=monitora_db

# Application Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=your_super_secret_jwt_key_here
FRONTEND_URL=http://localhost:3000

# Redis Configuration (if installed)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Configuration (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# API Keys (add your keys)
VIRUSTOTAL_API_KEY=your_virustotal_api_key
META_ACCESS_TOKEN=your_meta_access_token
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
GOOGLE_SEARCH_API_KEY=your_google_search_api_key

# Screenshot Configuration
SCREENSHOT_STORAGE_PATH=./screenshots
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
```

### **5. Start the Application**

```bash
# Development mode (with hot reload)
npm run dev

# Production mode
npm start

# Background monitoring job (in separate terminal)
npm run monitor
```

### **6. Verify Installation**

```bash
# Check application health
curl http://localhost:3000/health

# Test screenshot capture
curl -X POST http://localhost:3000/api/screenshots/capture \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'
```

## 🛠️ **Development Tools**

### **Database Management**
```bash
# Connect to database
psql -h localhost -U monitora_user -d monitora_db

# Or use a GUI tool like pgAdmin, DBeaver, or TablePlus
```

### **Redis Management** (if using Redis)
```bash
# Connect to Redis
redis-cli

# Or use Redis Desktop Manager, RedisInsight
```

## 📁 **Directory Structure**

The application will create these directories:
```
monitora/monitora/
├── screenshots/     # Screenshot storage
├── reports/         # Generated reports
├── logs/           # Application logs
├── uploads/        # File uploads
└── backups/        # Database backups
```

## 🔧 **Troubleshooting**

### **Common Issues**

1. **Database connection error:**
```bash
# Check if PostgreSQL is running
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS

# Check database exists
psql -h localhost -U monitora_user -l
```

2. **Chrome/Puppeteer issues:**
```bash
# Install missing dependencies (Linux)
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget

# Set Chrome path in .env
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
```

3. **Port already in use:**
```bash
# Find what's using port 3000
lsof -i :3000

# Kill the process or change PORT in .env
```

4. **Permission issues:**
```bash
# Fix directory permissions
chmod 755 screenshots reports logs uploads backups
```

## 🚀 **Production Deployment**

### **Using PM2 (Process Manager)**
```bash
# Install PM2
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'monitora-app',
    script: 'npm',
    args: 'start',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }, {
    name: 'monitora-monitor',
    script: 'npm',
    args: 'run monitor',
    instances: 1,
    autorestart: true,
    watch: false,
    cron_restart: '0 0 * * *'
  }]
};
EOF

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

### **Using systemd (Linux)**
```bash
# Create systemd service
sudo tee /etc/systemd/system/monitora.service << EOF
[Unit]
Description=Monitora Brand Protection
After=network.target postgresql.service

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/monitora/monitora
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable monitora
sudo systemctl start monitora
sudo systemctl status monitora
```

## 📊 **Monitoring**

### **Application Logs**
```bash
# View logs
tail -f logs/monitora.log

# Or use PM2 logs
pm2 logs monitora-app
```

### **Database Monitoring**
```bash
# Check database size
psql -h localhost -U monitora_user -d monitora_db -c "SELECT pg_size_pretty(pg_database_size('monitora_db'));"

# Check active connections
psql -h localhost -U monitora_user -d monitora_db -c "SELECT count(*) FROM pg_stat_activity;"
```

## 🔄 **Backup & Maintenance**

### **Database Backup**
```bash
# Create backup
pg_dump -h localhost -U monitora_user monitora_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
psql -h localhost -U monitora_user monitora_db < backup_file.sql
```

### **Cleanup Old Files**
```bash
# Clean old screenshots (older than 30 days)
find screenshots/ -name "*.png" -mtime +30 -delete

# Clean old reports (older than 90 days)
find reports/ -name "*" -mtime +90 -delete

# Clean old logs (older than 7 days)
find logs/ -name "*.log" -mtime +7 -delete
```

This setup gives you full control over the application without Docker dependencies!
