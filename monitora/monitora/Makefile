# Monitora Brand Protection - Docker Management

.PHONY: help build up down logs shell db-shell redis-shell clean dev prod monitoring backup restore

# Check if Docker is installed
check-docker:
	@which docker > /dev/null || (echo "❌ Docker is not installed. Please install Docker first." && echo "📖 See DOCKER_SETUP.md for installation instructions." && exit 1)
	@docker compose version > /dev/null 2>&1 || (echo "❌ Docker Compose is not available. Please install Docker with Compose plugin." && echo "📖 See DOCKER_SETUP.md for installation instructions." && exit 1)
	@echo "✅ Docker and Docker Compose are available"

# Default target
help: ## Show this help message
	@echo "Monitora Brand Protection - Docker Commands"
	@echo "==========================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
dev: check-docker ## Start development environment with hot reload
	@echo "🚀 Starting development environment..."
	@docker compose -f docker-compose.dev.yml up --build

dev-down: ## Stop development environment
	@echo "🛑 Stopping development environment..."
	@docker compose -f docker-compose.dev.yml down

dev-logs: ## View development logs
	@docker compose -f docker-compose.dev.yml logs -f app-dev

# Production commands
prod: check-docker ## Start production environment
	@echo "🚀 Starting production environment..."
	@docker compose up -d --build

prod-down: ## Stop production environment
	@echo "🛑 Stopping production environment..."
	@docker compose down

prod-logs: ## View production logs
	@docker compose logs -f app

# Monitoring
monitoring: check-docker ## Start with monitoring stack (Prometheus + Grafana)
	@echo "📊 Starting with monitoring stack..."
	@docker compose --profile monitoring up -d --build

# Build commands
build: ## Build the application image
	@echo "🔨 Building application image..."
	@docker build -t monitora-app .

build-dev: ## Build development image
	@echo "🔨 Building development image..."
	@docker build -f Dockerfile.dev -t monitora-app-dev .

# Container management
up: prod ## Alias for prod

down: ## Stop all services
	@echo "🛑 Stopping all services..."
	@docker compose down
	@docker compose -f docker-compose.dev.yml down 2>/dev/null || true

restart: ## Restart all services
	@echo "🔄 Restarting services..."
	@make down
	@make up

# Logs and debugging
logs: ## View application logs
	@docker compose logs -f app

logs-all: ## View all service logs
	@docker compose logs -f

shell: ## Access application container shell
	@echo "🐚 Accessing application shell..."
	@docker compose exec app /bin/bash

shell-dev: ## Access development container shell
	@echo "🐚 Accessing development shell..."
	@docker compose -f docker-compose.dev.yml exec app-dev /bin/bash

# Database management
db-shell: ## Access PostgreSQL shell
	@echo "🗄️ Accessing database shell..."
	@docker compose exec postgres psql -U monitora_user -d monitora_db

db-backup: ## Create database backup
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@docker compose exec postgres pg_dump -U monitora_user monitora_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

db-restore: ## Restore database from backup (usage: make db-restore FILE=backup.sql)
	@echo "🔄 Restoring database from $(FILE)..."
	@docker compose exec -T postgres psql -U monitora_user monitora_db < $(FILE)
	@echo "✅ Database restored"

db-reset: ## Reset database (WARNING: destroys all data)
	@echo "⚠️ This will destroy all database data. Continue? [y/N]" && read ans && [ $${ans:-N} = y ]
	@docker compose down
	@docker volume rm monitora_postgres_data 2>/dev/null || true
	@docker compose up -d postgres
	@echo "✅ Database reset complete"

# Redis management
redis-shell: ## Access Redis shell
	@echo "🔴 Accessing Redis shell..."
	@docker compose exec redis redis-cli -a monitora_redis_password

redis-flush: ## Flush Redis cache
	@echo "🧹 Flushing Redis cache..."
	@docker compose exec redis redis-cli -a monitora_redis_password FLUSHALL
	@echo "✅ Redis cache flushed"

# Health checks
health: ## Check service health
	@echo "🏥 Checking service health..."
	@docker compose ps
	@echo ""
	@echo "Application health:"
	@curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health

test-services: ## Test all service connections
	@echo "🧪 Testing service connections..."
	@echo "App health:"
	@curl -s http://localhost:3000/health || echo "❌ App not responding"
	@echo ""
	@echo "Database connection:"
	@docker compose exec postgres pg_isready -U monitora_user || echo "❌ Database not ready"
	@echo ""
	@echo "Redis connection:"
	@docker compose exec redis redis-cli -a monitora_redis_password ping || echo "❌ Redis not responding"

# Cleanup commands
clean: ## Clean up containers, images, and volumes
	@echo "🧹 Cleaning up Docker resources..."
	@docker compose down -v
	@docker compose -f docker-compose.dev.yml down -v 2>/dev/null || true
	@docker system prune -f
	@echo "✅ Cleanup complete"

clean-all: ## Clean up everything including volumes (WARNING: destroys all data)
	@echo "⚠️ This will destroy ALL data including database. Continue? [y/N]" && read ans && [ $${ans:-N} = y ]
	@docker compose down -v
	@docker compose -f docker-compose.dev.yml down -v 2>/dev/null || true
	@docker system prune -a -f --volumes
	@echo "✅ Complete cleanup finished"

# Setup commands
setup: ## Initial setup - copy environment file
	@echo "⚙️ Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.docker .env; \
		echo "✅ Environment file created (.env)"; \
		echo "📝 Please edit .env with your API keys and configuration"; \
	else \
		echo "⚠️ .env file already exists"; \
	fi

setup-dev: ## Setup development environment
	@echo "⚙️ Setting up development environment..."
	@make setup
	@make dev

# Utility commands
ps: ## Show running containers
	@docker compose ps

stats: ## Show container resource usage
	@docker stats --no-stream

volumes: ## List Docker volumes
	@docker volume ls | grep monitora

networks: ## List Docker networks
	@docker network ls | grep monitora

# Update commands
update: ## Pull latest images and restart
	@echo "🔄 Updating images and restarting..."
	@docker compose pull
	@docker compose up -d --build

# SSL setup (for production)
ssl-setup: ## Setup SSL certificates (requires manual certificate placement)
	@echo "🔒 Setting up SSL..."
	@mkdir -p docker/nginx/ssl
	@echo "📝 Place your SSL certificates in docker/nginx/ssl/"
	@echo "   - cert.pem (certificate)"
	@echo "   - key.pem (private key)"
	@echo "📝 Then uncomment HTTPS server block in docker/nginx/nginx.conf"

# Quick commands for common tasks
quick-start: setup prod ## Quick start for new installations
	@echo "🎉 Monitora is starting up!"
	@echo "📊 Access the application at: http://localhost:3000"
	@echo "🏥 Health check: http://localhost:3000/health"

quick-dev: setup-dev ## Quick development setup
	@echo "🎉 Development environment is ready!"
	@echo "📊 Application: http://localhost:3000"
	@echo "🗄️ pgAdmin: http://localhost:5050"
	@echo "📧 MailHog: http://localhost:8025"

# Documentation
docs: ## Open documentation
	@echo "📚 Opening documentation..."
	@echo "Docker Setup: file://$(PWD)/DOCKER_SETUP.md"
	@echo "Implementation Status: file://$(PWD)/MISSING_IMPLEMENTATIONS.md"
	@echo "Brand Monitoring Guide: file://$(PWD)/BRAND_MONITORING_GUIDE.md"
