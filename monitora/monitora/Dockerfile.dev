# Development Dockerfile with hot reload
FROM node:18-slim

# Install system dependencies for Puppeteer and development tools
RUN apt-get update \
    && apt-get install -y wget gnupg curl \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/app

# Create non-root user for security
RUN groupadd -r monitora && useradd -r -g monitora -G audio,video monitora \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R monitora:monitora /home/<USER>
    && chown -R monitora:monitora /usr/src/app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install && npm cache clean --force

# Create necessary directories
RUN mkdir -p screenshots reports logs uploads backups \
    && chown -R monitora:monitora screenshots reports logs uploads backups

# Switch to non-root user
USER monitora

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Expose ports
EXPOSE 3000 9229

# Start the application in development mode with debugging
CMD ["npm", "run", "dev"]
