import { DataSource } from "typeorm";
import { Contact } from "../entities/contact.entity";
import { ContactAddress } from "../entities/contact-address.entity";
import { ContactSocialProfile } from "../entities/contact-social-profile.entity";
import { Product } from "../entities/product.entity";
import { ProductImage } from "../entities/product-image.entity";
import { ProductFeature } from "../entities/product-feature.entity";
import { User } from "../entities/user.entity";
import { Brand } from "../entities/brand.entity";
import { DomainMonitor } from "../entities/domain-monitor.entity";
import { SocialMediaMonitor } from "../entities/social-media-monitor.entity";
import { ThreatDetection } from "../entities/threat-detection.entity";
import { Alert } from "../entities/alert.entity";
import { TakedownRequest } from "../entities/takedown-request.entity";
import { MonitoringRule } from "../entities/monitoring-rule.entity";
import { Role } from "../entities/role.entity";
import { Permission } from "../entities/permission.entity";
import { Lead } from "../entities/lead.entity";
import { Opportunity } from "../entities/opportunity.entity";
import { Task } from "../entities/task.entity";
import { PipelineStage } from "../entities/pipeline-stage.entity";
import * as dotenv from "dotenv";

dotenv.config();

export const AppDataSource = new DataSource({
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 5432,
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "postgres",
  database: process.env.DB_DATABASE || "monitora_db",
  synchronize: process.env.NODE_ENV !== "production",
  logging: process.env.NODE_ENV !== "production",
  entities: [
    Contact,
    ContactAddress,
    ContactSocialProfile,
    Product,
    ProductImage,
    ProductFeature,
    User,
    Brand,
    DomainMonitor,
    SocialMediaMonitor,
    ThreatDetection,
    Alert,
    TakedownRequest,
    MonitoringRule,
    Role,
    Permission,
    Lead,
    Opportunity,
    Task,
    PipelineStage,
  ],
  migrations: ["src/migrations/**/*.ts"],
  subscribers: ["src/subscribers/**/*.ts"],
});
