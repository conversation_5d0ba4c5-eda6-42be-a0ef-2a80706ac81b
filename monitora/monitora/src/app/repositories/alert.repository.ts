import { Provider } from "@core/decorators/decorators";
import { BaseRepository } from "@core/repositories/base.repository";
import { Alert } from "../../entities/alert.entity";
import { Mock } from "@core/decorators/mock.decorator";

@Provider()
export class AlertRepository extends BaseRepository<Alert> {
  constructor() {
    super(Alert);
  }

  @Mock("AlertRepository-findByBrand")
  async findByBrand(brandId: string): Promise<Alert[]> {
    return this.repository.find({
      where: { brandId },
      relations: ["brand", "threatDetection", "recipient"],
      order: { createdAt: "DESC" },
    });
  }

  @Mock("AlertRepository-findUnreadByUser")
  async findUnreadByUser(userId: string): Promise<Alert[]> {
    return this.repository.find({
      where: {
        recipientId: userId,
        isRead: false,
        isArchived: false,
      },
      relations: ["brand", "threatDetection"],
      order: { createdAt: "DESC" },
    });
  }

  @Mock("AlertRepository-findByStatus")
  async findByStatus(status: Alert["status"]): Promise<Alert[]> {
    return this.repository.find({
      where: { status },
      relations: ["brand", "threatDetection"],
      order: { createdAt: "DESC" },
    });
  }

  @Mock("AlertRepository-markAsRead")
  async markAsRead(alertIds: string[]): Promise<void> {
    await this.repository
      .createQueryBuilder()
      .update(Alert)
      .set({
        isRead: true,
        deliveryDetails: {
          acknowledgedAt: new Date(),
        },
      })
      .where("id IN (:...ids)", { ids: alertIds })
      .execute();
  }

  @Mock("AlertRepository-findOldRead")
  async findOldRead(cutoffDate: Date): Promise<Alert[]> {
    return this.repository
      .createQueryBuilder("alert")
      .where("alert.isRead = :isRead", { isRead: true })
      .andWhere("alert.createdAt < :cutoffDate", { cutoffDate })
      .getMany();
  }
}
