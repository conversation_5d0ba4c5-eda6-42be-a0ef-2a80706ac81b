import { Provider } from "@core/decorators/decorators";
import { BaseRepository } from "@core/repositories/base.repository";
import { ThreatDetection } from "../../entities/threat-detection.entity";
import { Mock } from "@core/decorators/mock.decorator";

@Provider()
export class ThreatDetectionRepository extends BaseRepository<ThreatDetection> {
  constructor() {
    super(ThreatDetection);
  }

  @Mock("ThreatDetectionRepository-findByBrand")
  async findByBrand(
    brandId: string,
    filters?: {
      status?: ThreatDetection["status"];
      severity?: ThreatDetection["severity"];
      threatType?: ThreatDetection["threatType"];
      sourceType?: ThreatDetection["sourceType"];
    }
  ): Promise<ThreatDetection[]> {
    const query = this.repository
      .createQueryBuilder("threat")
      .leftJoinAndSelect("threat.brand", "brand")
      .leftJoinAndSelect("threat.domainMonitor", "domain")
      .leftJoinAndSelect("threat.socialMediaMonitor", "social")
      .leftJoinAndSelect("threat.alerts", "alerts")
      .leftJoinAndSelect("threat.takedownRequests", "takedowns")
      .where("threat.brandId = :brandId", { brandId });

    if (filters?.status) {
      query.andWhere("threat.status = :status", { status: filters.status });
    }

    if (filters?.severity) {
      query.andWhere("threat.severity = :severity", {
        severity: filters.severity,
      });
    }

    if (filters?.threatType) {
      query.andWhere("threat.threatType = :threatType", {
        threatType: filters.threatType,
      });
    }

    if (filters?.sourceType) {
      query.andWhere("threat.sourceType = :sourceType", {
        sourceType: filters.sourceType,
      });
    }

    return query
      .orderBy("threat.severity", "DESC")
      .addOrderBy("threat.firstDetected", "DESC")
      .getMany();
  }

  @Mock("ThreatDetectionRepository-findByStatus")
  async findByStatus(
    status: ThreatDetection["status"]
  ): Promise<ThreatDetection[]> {
    return this.repository.find({
      where: { status },
      relations: ["brand", "domainMonitor", "socialMediaMonitor"],
      order: { firstDetected: "DESC" },
    });
  }

  @Mock("ThreatDetectionRepository-findBySeverity")
  async findBySeverity(
    severity: ThreatDetection["severity"]
  ): Promise<ThreatDetection[]> {
    return this.repository.find({
      where: { severity },
      relations: ["brand", "domainMonitor", "socialMediaMonitor"],
      order: { firstDetected: "DESC" },
    });
  }

  @Mock("ThreatDetectionRepository-findHighPriorityThreats")
  async findHighPriorityThreats(): Promise<ThreatDetection[]> {
    return this.repository
      .createQueryBuilder("threat")
      .leftJoinAndSelect("threat.brand", "brand")
      .leftJoinAndSelect("threat.domainMonitor", "domain")
      .leftJoinAndSelect("threat.socialMediaMonitor", "social")
      .where("threat.severity IN (:...severities)", {
        severities: ["high", "critical"],
      })
      .andWhere("threat.status IN (:...statuses)", {
        statuses: ["pending", "investigating", "confirmed"],
      })
      .orderBy("threat.severity", "DESC")
      .addOrderBy("threat.confidenceScore", "DESC")
      .getMany();
  }

  @Mock("ThreatDetectionRepository-findRecentThreats")
  async findRecentThreats(hoursAgo: number = 24): Promise<ThreatDetection[]> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hoursAgo);

    return this.repository
      .createQueryBuilder("threat")
      .leftJoinAndSelect("threat.brand", "brand")
      .where("threat.firstDetected >= :cutoffDate", { cutoffDate })
      .orderBy("threat.firstDetected", "DESC")
      .getMany();
  }

  @Mock("ThreatDetectionRepository-getStats")
  async getStats(brandId?: string): Promise<{
    total: number;
    byStatus: { [key: string]: number };
    bySeverity: { [key: string]: number };
    byThreatType: { [key: string]: number };
    bySourceType: { [key: string]: number };
  }> {
    const query = this.repository.createQueryBuilder("threat");

    if (brandId) {
      query.where("threat.brandId = :brandId", { brandId });
    }

    const threats = await query.getMany();

    const stats = {
      total: threats.length,
      byStatus: {} as { [key: string]: number },
      bySeverity: {} as { [key: string]: number },
      byThreatType: {} as { [key: string]: number },
      bySourceType: {} as { [key: string]: number },
    };

    threats.forEach((threat) => {
      // Count by status
      stats.byStatus[threat.status] = (stats.byStatus[threat.status] || 0) + 1;

      // Count by severity
      stats.bySeverity[threat.severity] =
        (stats.bySeverity[threat.severity] || 0) + 1;

      // Count by threat type
      stats.byThreatType[threat.threatType] =
        (stats.byThreatType[threat.threatType] || 0) + 1;

      // Count by source type
      stats.bySourceType[threat.sourceType] =
        (stats.bySourceType[threat.sourceType] || 0) + 1;
    });

    return stats;
  }

  @Mock("ThreatDetectionRepository-findThreatsNeedingAttention")
  async findThreatsNeedingAttention(): Promise<ThreatDetection[]> {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    return this.repository
      .createQueryBuilder("threat")
      .leftJoinAndSelect("threat.brand", "brand")
      .where("threat.status = :status", { status: "pending" })
      .andWhere("threat.firstDetected < :oneDayAgo", { oneDayAgo })
      .orWhere("threat.severity IN (:...severities)", {
        severities: ["high", "critical"],
      })
      .andWhere("threat.status IN (:...statuses)", {
        statuses: ["pending", "investigating"],
      })
      .orderBy("threat.severity", "DESC")
      .addOrderBy("threat.firstDetected", "ASC")
      .getMany();
  }

  @Mock("ThreatDetectionRepository-searchThreats")
  async searchThreats(
    query: string,
    brandId?: string
  ): Promise<ThreatDetection[]> {
    const queryBuilder = this.repository
      .createQueryBuilder("threat")
      .leftJoinAndSelect("threat.brand", "brand")
      .leftJoinAndSelect("threat.domainMonitor", "domain")
      .leftJoinAndSelect("threat.socialMediaMonitor", "social")
      .where("threat.description ILIKE :query", { query: `%${query}%` })
      .orWhere("threat.sourceUrl ILIKE :query", { query: `%${query}%` });

    if (brandId) {
      queryBuilder.andWhere("threat.brandId = :brandId", { brandId });
    }

    return queryBuilder
      .orderBy("threat.severity", "DESC")
      .addOrderBy("threat.firstDetected", "DESC")
      .getMany();
  }

  @Mock("ThreatDetectionRepository-updateBulkStatus")
  async updateBulkStatus(
    threatIds: string[],
    status: ThreatDetection["status"]
  ): Promise<void> {
    const updateData: any = { status, lastUpdated: new Date() };

    if (status === "resolved") {
      updateData.resolvedAt = new Date();
    }

    await this.repository
      .createQueryBuilder()
      .update(ThreatDetection)
      .set(updateData)
      .where("id IN (:...ids)", { ids: threatIds })
      .execute();
  }

  @Mock("ThreatDetectionRepository-getThreatTimeline")
  async getThreatTimeline(
    brandId: string,
    days: number = 30
  ): Promise<
    {
      date: string;
      count: number;
      severity: { [key: string]: number };
    }[]
  > {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const threats = await this.repository
      .createQueryBuilder("threat")
      .where("threat.brandId = :brandId", { brandId })
      .andWhere("threat.firstDetected >= :startDate", { startDate })
      .orderBy("threat.firstDetected", "ASC")
      .getMany();

    // Group by date
    const timeline: {
      [key: string]: { count: number; severity: { [key: string]: number } };
    } = {};

    threats.forEach((threat) => {
      const date = threat.firstDetected.toISOString().split("T")[0];

      if (!timeline[date]) {
        timeline[date] = { count: 0, severity: {} };
      }

      timeline[date].count++;
      timeline[date].severity[threat.severity] =
        (timeline[date].severity[threat.severity] || 0) + 1;
    });

    return Object.entries(timeline).map(([date, data]) => ({
      date,
      count: data.count,
      severity: data.severity,
    }));
  }

  @Mock("ThreatDetectionRepository-findOldResolved")
  async findOldResolved(cutoffDate: Date): Promise<ThreatDetection[]> {
    return this.repository
      .createQueryBuilder("threat")
      .where("threat.status = :status", { status: "resolved" })
      .andWhere("threat.resolvedAt < :cutoffDate", { cutoffDate })
      .getMany();
  }
}
