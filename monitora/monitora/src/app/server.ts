import express from "express";
import { createServer } from "http";
import cors from "cors";
import { routes } from "../core/routes";
import { container } from "@core/di/container";
import { AppDataSource } from "../config/database";
import { errorHandler } from "./middlewares/error.middleware";
import { WebSocketService } from "./services/websocket.service";
import * as path from "path";

declare global {
  namespace Express {
    interface Request {
      currentUser: { id: string; username: string };
    }
  }
}

const app = express();
const httpServer = createServer(app);

// Initialize dependency injection
container.register();

// Middleware
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  })
);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Serve static files (screenshots, reports, etc.)
app.use(
  "/api/screenshots",
  express.static(path.join(process.cwd(), "screenshots"))
);
app.use("/api/reports", express.static(path.join(process.cwd(), "reports")));

// API routes
app.use("/api", routes());

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || "1.0.0",
  });
});

// Error handling middleware should be last
app.use(errorHandler);

// Initialize WebSocket service
const webSocketService = container.resolve(WebSocketService);

// Initialize TypeORM connection and start server
AppDataSource.initialize()
  .then(() => {
    console.log("Database connection initialized");

    // Initialize WebSocket
    webSocketService.initialize(httpServer);

    const port = process.env.PORT || 3000;
    httpServer.listen(port, () => {
      console.log(`🚀 Server running on port ${port}`);
      console.log(`📊 Health check: http://localhost:${port}/health`);
      console.log(`🔌 WebSocket server initialized`);
      console.log(
        `📁 Static files served from /api/screenshots and /api/reports`
      );
    });
  })
  .catch((error) => {
    console.error("Error during Data Source initialization:", error);
    process.exit(1);
  });

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  httpServer.close(() => {
    console.log("Process terminated");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  httpServer.close(() => {
    console.log("Process terminated");
    process.exit(0);
  });
});
