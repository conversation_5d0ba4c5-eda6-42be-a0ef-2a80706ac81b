import { Provider } from '@core/decorators/decorators';
import { Mo<PERSON> } from '@core/decorators/mock.decorator';
import puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';

@Provider()
export class ScreenshotService {
  private screenshotDir = path.join(process.cwd(), 'screenshots');

  constructor() {
    // Ensure screenshot directory exists
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }
  }

  @Mock('ScreenshotService-captureWebsite')
  async captureWebsite(url: string, options: {
    width?: number;
    height?: number;
    fullPage?: boolean;
    quality?: number;
  } = {}): Promise<{
    filename: string;
    path: string;
    size: number;
    timestamp: Date;
  }> {
    const {
      width = 1920,
      height = 1080,
      fullPage = true,
      quality = 90
    } = options;

    let browser;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();
      
      // Set viewport
      await page.setViewport({ width, height });
      
      // Set user agent to avoid bot detection
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // Navigate to URL with timeout
      await page.goto(url, { 
        waitUntil: 'networkidle2', 
        timeout: 30000 
      });

      // Wait for page to load completely
      await page.waitForTimeout(2000);

      // Generate filename
      const timestamp = new Date();
      const domain = new URL(url).hostname;
      const filename = `${domain}_${timestamp.getTime()}.png`;
      const filepath = path.join(this.screenshotDir, filename);

      // Take screenshot
      await page.screenshot({
        path: filepath,
        fullPage,
        quality,
        type: 'png'
      });

      // Get file size
      const stats = fs.statSync(filepath);

      return {
        filename,
        path: filepath,
        size: stats.size,
        timestamp
      };

    } catch (error) {
      console.error(`Error capturing screenshot for ${url}:`, error);
      throw new Error(`Failed to capture screenshot: ${error.message}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  @Mock('ScreenshotService-captureMultiple')
  async captureMultiple(urls: string[], options = {}): Promise<Array<{
    url: string;
    success: boolean;
    screenshot?: any;
    error?: string;
  }>> {
    const results = [];

    for (const url of urls) {
      try {
        const screenshot = await this.captureWebsite(url, options);
        results.push({
          url,
          success: true,
          screenshot
        });
      } catch (error) {
        results.push({
          url,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  @Mock('ScreenshotService-captureWithMetadata')
  async captureWithMetadata(url: string, metadata: any = {}): Promise<{
    screenshot: any;
    metadata: any;
    pageInfo: any;
  }> {
    let browser;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();
      await page.setViewport({ width: 1920, height: 1080 });
      
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

      // Extract page metadata
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          description: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
          keywords: document.querySelector('meta[name="keywords"]')?.getAttribute('content') || '',
          ogTitle: document.querySelector('meta[property="og:title"]')?.getAttribute('content') || '',
          ogDescription: document.querySelector('meta[property="og:description"]')?.getAttribute('content') || '',
          ogImage: document.querySelector('meta[property="og:image"]')?.getAttribute('content') || '',
          favicon: document.querySelector('link[rel="icon"]')?.getAttribute('href') || 
                   document.querySelector('link[rel="shortcut icon"]')?.getAttribute('href') || '',
          canonicalUrl: document.querySelector('link[rel="canonical"]')?.getAttribute('href') || '',
          language: document.documentElement.lang || '',
          charset: document.characterSet || '',
          viewport: document.querySelector('meta[name="viewport"]')?.getAttribute('content') || ''
        };
      });

      const screenshot = await this.captureWebsite(url);

      return {
        screenshot,
        metadata: {
          ...metadata,
          capturedAt: new Date(),
          userAgent: await page.evaluate(() => navigator.userAgent),
          url: page.url()
        },
        pageInfo
      };

    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  @Mock('ScreenshotService-deleteScreenshot')
  async deleteScreenshot(filename: string): Promise<boolean> {
    try {
      const filepath = path.join(this.screenshotDir, filename);
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Error deleting screenshot ${filename}:`, error);
      return false;
    }
  }

  @Mock('ScreenshotService-cleanupOldScreenshots')
  async cleanupOldScreenshots(daysOld: number = 30): Promise<number> {
    try {
      const files = fs.readdirSync(this.screenshotDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      let deletedCount = 0;

      for (const file of files) {
        const filepath = path.join(this.screenshotDir, file);
        const stats = fs.statSync(filepath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filepath);
          deletedCount++;
        }
      }

      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up old screenshots:', error);
      return 0;
    }
  }

  @Mock('ScreenshotService-getScreenshotPath')
  getScreenshotPath(filename: string): string {
    return path.join(this.screenshotDir, filename);
  }

  @Mock('ScreenshotService-getScreenshotUrl')
  getScreenshotUrl(filename: string): string {
    // Return URL for serving screenshots via API
    return `/api/screenshots/${filename}`;
  }
}
