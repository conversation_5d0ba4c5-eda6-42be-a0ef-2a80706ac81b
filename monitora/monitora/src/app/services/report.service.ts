import { Provider } from "@core/decorators/decorators";
import { Mock } from "@core/decorators/mock.decorator";
import { BrandService } from "./brand.service";
import { ThreatDetectionService } from "./threat-detection.service";
import { DomainMonitoringService } from "./domain-monitoring.service";
import { SocialMediaMonitoringService } from "./social-media-monitoring.service";
import { Brand } from "../../entities/brand.entity";
import * as fs from "fs";
import * as path from "path";

interface ReportData {
  brand: Brand;
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalThreats: number;
    criticalThreats: number;
    resolvedThreats: number;
    domainsMonitored: number;
    socialMediaMonitored: number;
  };
  threats: any[];
  domains: any[];
  socialMedia: any[];
  recommendations: string[];
}

interface ReportOptions {
  format: "json" | "csv" | "pdf" | "html";
  period: "daily" | "weekly" | "monthly" | "custom";
  startDate?: Date;
  endDate?: Date;
  includeScreenshots?: boolean;
  includeDetails?: boolean;
}

@Provider()
export class ReportService {
  private reportsDir = path.join(process.cwd(), "reports");

  constructor(
    private brandService: BrandService,
    private threatDetectionService: ThreatDetectionService,
    private domainMonitoringService: DomainMonitoringService,
    private socialMediaMonitoringService: SocialMediaMonitoringService
  ) {
    // Ensure reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  @Mock("ReportService-generateBrandReport")
  async generateBrandReport(
    brandId: string,
    options: ReportOptions
  ): Promise<{
    filename: string;
    path: string;
    size: number;
    format: string;
  }> {
    try {
      // Get brand information
      const brand = await this.brandService.findById(brandId);
      if (!brand) {
        throw new Error(`Brand not found: ${brandId}`);
      }

      // Calculate date range
      const { start, end } = this.calculateDateRange(options);

      // Gather report data
      const reportData = await this.gatherReportData(
        brand,
        start,
        end,
        options
      );

      // Generate report based on format
      let filename: string;
      let content: string | Buffer;

      switch (options.format) {
        case "json":
          filename = `${brand.name}_report_${this.formatDate(
            start
          )}_${this.formatDate(end)}.json`;
          content = JSON.stringify(reportData, null, 2);
          break;
        case "csv":
          filename = `${brand.name}_report_${this.formatDate(
            start
          )}_${this.formatDate(end)}.csv`;
          content = this.generateCSVReport(reportData);
          break;
        case "html":
          filename = `${brand.name}_report_${this.formatDate(
            start
          )}_${this.formatDate(end)}.html`;
          content = this.generateHTMLReport(reportData);
          break;
        case "pdf":
          filename = `${brand.name}_report_${this.formatDate(
            start
          )}_${this.formatDate(end)}.pdf`;
          content = await this.generatePDFReport(reportData);
          break;
        default:
          throw new Error(`Unsupported format: ${options.format}`);
      }

      // Save report to file
      const filepath = path.join(this.reportsDir, filename);
      fs.writeFileSync(filepath, content);

      // Get file size
      const stats = fs.statSync(filepath);

      return {
        filename,
        path: filepath,
        size: stats.size,
        format: options.format,
      };
    } catch (error) {
      console.error("Error generating brand report:", error);
      throw error;
    }
  }

  @Mock("ReportService-generateSystemReport")
  async generateSystemReport(options: ReportOptions): Promise<{
    filename: string;
    path: string;
    size: number;
    format: string;
  }> {
    try {
      const { start, end } = this.calculateDateRange(options);

      // Get all brands
      const brands = await this.brandService.findAll();

      // Gather system-wide data
      const systemData = {
        period: { start, end },
        summary: {
          totalBrands: brands.length,
          totalThreats: 0,
          criticalThreats: 0,
          resolvedThreats: 0,
          totalDomains: 0,
          totalSocialMedia: 0,
        },
        brandReports: [] as Array<{
          brandId: string;
          brandName: string;
          summary: {
            totalThreats: number;
            criticalThreats: number;
            resolvedThreats: number;
            domainsMonitored: number;
            socialMediaMonitored: number;
          };
        }>,
      };

      // Aggregate data from all brands
      for (const brand of brands) {
        const brandData = await this.gatherReportData(
          brand,
          start,
          end,
          options
        );
        systemData.summary.totalThreats += brandData.summary.totalThreats;
        systemData.summary.criticalThreats += brandData.summary.criticalThreats;
        systemData.summary.resolvedThreats += brandData.summary.resolvedThreats;
        systemData.summary.totalDomains += brandData.summary.domainsMonitored;
        systemData.summary.totalSocialMedia +=
          brandData.summary.socialMediaMonitored;

        systemData.brandReports.push({
          brandId: brand.id,
          brandName: brand.name,
          summary: brandData.summary,
        });
      }

      // Generate report
      const filename = `system_report_${this.formatDate(
        start
      )}_${this.formatDate(end)}.${options.format}`;
      let content: string | Buffer;

      switch (options.format) {
        case "json":
          content = JSON.stringify(systemData, null, 2);
          break;
        case "csv":
          content = this.generateSystemCSVReport(systemData);
          break;
        case "html":
          content = this.generateSystemHTMLReport(systemData);
          break;
        default:
          throw new Error(
            `Unsupported format for system report: ${options.format}`
          );
      }

      const filepath = path.join(this.reportsDir, filename);
      fs.writeFileSync(filepath, content);
      const stats = fs.statSync(filepath);

      return {
        filename,
        path: filepath,
        size: stats.size,
        format: options.format,
      };
    } catch (error) {
      console.error("Error generating system report:", error);
      throw error;
    }
  }

  private async gatherReportData(
    brand: Brand,
    start: Date,
    end: Date,
    options: ReportOptions
  ): Promise<ReportData> {
    // Get threats for the period
    const threats = await this.threatDetectionService.getThreatsForBrand(
      brand.id
    );
    const periodThreats = threats.filter(
      (threat) => threat.createdAt >= start && threat.createdAt <= end
    );

    // Get domain monitors
    const domains = await this.domainMonitoringService.getDomainMonitors(
      brand.id
    );
    const periodDomains = domains.filter(
      (domain) => domain.createdAt >= start && domain.createdAt <= end
    );

    // Get social media monitors
    const socialMedia =
      await this.socialMediaMonitoringService.getSocialMediaMonitors(brand.id);
    const periodSocialMedia = socialMedia.filter(
      (sm) => sm.createdAt >= start && sm.createdAt <= end
    );

    // Calculate summary
    const summary = {
      totalThreats: periodThreats.length,
      criticalThreats: periodThreats.filter((t) => t.severity === "critical")
        .length,
      resolvedThreats: periodThreats.filter((t) => t.status === "resolved")
        .length,
      domainsMonitored: periodDomains.length,
      socialMediaMonitored: periodSocialMedia.length,
    };

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      summary,
      periodThreats
    );

    return {
      brand,
      period: { start, end },
      summary,
      threats: options.includeDetails
        ? periodThreats
        : periodThreats.map((t) => ({
            id: t.id,
            threatType: t.threatType,
            severity: t.severity,
            status: t.status,
            sourceUrl: t.sourceUrl,
            createdAt: t.createdAt,
          })),
      domains: options.includeDetails
        ? periodDomains
        : periodDomains.map((d) => ({
            id: d.id,
            domain: d.domain,
            status: d.status,
            similarityScore: d.similarityScore,
            createdAt: d.createdAt,
          })),
      socialMedia: options.includeDetails
        ? periodSocialMedia
        : periodSocialMedia.map((sm) => ({
            id: sm.id,
            platform: sm.platform,
            contentType: sm.contentType,
            status: sm.status,
            createdAt: sm.createdAt,
          })),
      recommendations,
    };
  }

  private calculateDateRange(options: ReportOptions): {
    start: Date;
    end: Date;
  } {
    const end = options.endDate || new Date();
    let start: Date;

    if (options.period === "custom" && options.startDate) {
      start = options.startDate;
    } else {
      const now = new Date();
      switch (options.period) {
        case "daily":
          start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case "weekly":
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "monthly":
          start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }
    }

    return { start, end };
  }

  private generateRecommendations(summary: any, threats: any[]): string[] {
    const recommendations = [];

    if (summary.criticalThreats > 0) {
      recommendations.push(
        `Address ${summary.criticalThreats} critical threats immediately`
      );
    }

    if (summary.totalThreats > 10) {
      recommendations.push(
        "Consider increasing monitoring frequency due to high threat volume"
      );
    }

    const unresolvedThreats = summary.totalThreats - summary.resolvedThreats;
    if (unresolvedThreats > 5) {
      recommendations.push(
        `${unresolvedThreats} threats remain unresolved - review and take action`
      );
    }

    if (summary.domainsMonitored > 20) {
      recommendations.push(
        "High number of suspicious domains detected - consider domain protection services"
      );
    }

    return recommendations;
  }

  private generateCSVReport(data: ReportData): string {
    // Simple CSV generation - in production, use a proper CSV library
    let csv = "Type,ID,Description,Severity,Status,Date\n";

    data.threats.forEach((threat) => {
      csv += `Threat,${threat.id},${threat.threatType},${threat.severity},${threat.status},${threat.createdAt}\n`;
    });

    data.domains.forEach((domain) => {
      csv += `Domain,${domain.id},${domain.domain},${domain.similarityScore},${domain.status},${domain.createdAt}\n`;
    });

    return csv;
  }

  private generateHTMLReport(data: ReportData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Brand Monitoring Report - ${data.brand.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
          .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
          .metric { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
          .metric h3 { margin: 0; color: #333; }
          .metric .value { font-size: 2em; font-weight: bold; color: #007bff; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Brand Monitoring Report</h1>
          <h2>${data.brand.name}</h2>
          <p>Period: ${data.period.start.toLocaleDateString()} - ${data.period.end.toLocaleDateString()}</p>
        </div>

        <div class="summary">
          <div class="metric">
            <h3>Total Threats</h3>
            <div class="value">${data.summary.totalThreats}</div>
          </div>
          <div class="metric">
            <h3>Critical Threats</h3>
            <div class="value">${data.summary.criticalThreats}</div>
          </div>
          <div class="metric">
            <h3>Resolved Threats</h3>
            <div class="value">${data.summary.resolvedThreats}</div>
          </div>
          <div class="metric">
            <h3>Domains Monitored</h3>
            <div class="value">${data.summary.domainsMonitored}</div>
          </div>
        </div>

        <h3>Recommendations</h3>
        <ul>
          ${data.recommendations.map((rec) => `<li>${rec}</li>`).join("")}
        </ul>

        <h3>Recent Threats</h3>
        <table>
          <tr><th>Type</th><th>Severity</th><th>Status</th><th>Source</th><th>Date</th></tr>
          ${data.threats
            .slice(0, 10)
            .map(
              (threat) => `
            <tr>
              <td>${threat.threatType}</td>
              <td>${threat.severity}</td>
              <td>${threat.status}</td>
              <td>${threat.sourceUrl}</td>
              <td>${new Date(threat.createdAt).toLocaleDateString()}</td>
            </tr>
          `
            )
            .join("")}
        </table>
      </body>
      </html>
    `;
  }

  private async generatePDFReport(data: ReportData): Promise<Buffer> {
    // For now, return HTML as buffer - in production, use puppeteer or similar to generate PDF
    const html = this.generateHTMLReport(data);
    return Buffer.from(html, "utf8");
  }

  private generateSystemCSVReport(data: any): string {
    let csv =
      "Brand,Total Threats,Critical Threats,Resolved Threats,Domains,Social Media\n";

    data.brandReports.forEach((brand: any) => {
      csv += `${brand.brandName},${brand.summary.totalThreats},${brand.summary.criticalThreats},${brand.summary.resolvedThreats},${brand.summary.domainsMonitored},${brand.summary.socialMediaMonitored}\n`;
    });

    return csv;
  }

  private generateSystemHTMLReport(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>System Monitoring Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
          .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
          .metric { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>System Monitoring Report</h1>
          <p>Period: ${data.period.start.toLocaleDateString()} - ${data.period.end.toLocaleDateString()}</p>
        </div>

        <div class="summary">
          <div class="metric">
            <h3>Total Brands</h3>
            <div class="value">${data.summary.totalBrands}</div>
          </div>
          <div class="metric">
            <h3>Total Threats</h3>
            <div class="value">${data.summary.totalThreats}</div>
          </div>
          <div class="metric">
            <h3>Critical Threats</h3>
            <div class="value">${data.summary.criticalThreats}</div>
          </div>
        </div>

        <h3>Brand Summary</h3>
        <table>
          <tr><th>Brand</th><th>Threats</th><th>Critical</th><th>Resolved</th><th>Domains</th></tr>
          ${data.brandReports
            .map(
              (brand: any) => `
            <tr>
              <td>${brand.brandName}</td>
              <td>${brand.summary.totalThreats}</td>
              <td>${brand.summary.criticalThreats}</td>
              <td>${brand.summary.resolvedThreats}</td>
              <td>${brand.summary.domainsMonitored}</td>
            </tr>
          `
            )
            .join("")}
        </table>
      </body>
      </html>
    `;
  }

  private formatDate(date: Date): string {
    return date.toISOString().split("T")[0];
  }

  @Mock("ReportService-getReportHistory")
  async getReportHistory(): Promise<
    Array<{
      filename: string;
      size: number;
      created: Date;
      format: string;
    }>
  > {
    try {
      const files = fs.readdirSync(this.reportsDir);
      const reports = [];

      for (const file of files) {
        const filepath = path.join(this.reportsDir, file);
        const stats = fs.statSync(filepath);
        const format = path.extname(file).substring(1);

        reports.push({
          filename: file,
          size: stats.size,
          created: stats.birthtime,
          format,
        });
      }

      return reports.sort((a, b) => b.created.getTime() - a.created.getTime());
    } catch (error) {
      console.error("Error getting report history:", error);
      return [];
    }
  }

  @Mock("ReportService-deleteReport")
  async deleteReport(filename: string): Promise<boolean> {
    try {
      const filepath = path.join(this.reportsDir, filename);
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error deleting report:", error);
      return false;
    }
  }

  @Mock("ReportService-generateSalesPipeline")
  async generateSalesPipeline(): Promise<any> {
    // Placeholder implementation for sales pipeline report
    // This would integrate with your CRM/sales data
    return {
      totalOpportunities: 0,
      totalValue: 0,
      stages: [],
      conversionRates: {},
      generatedAt: new Date(),
    };
  }

  @Mock("ReportService-generateLeadConversionReport")
  async generateLeadConversionReport(): Promise<any> {
    // Placeholder implementation for lead conversion report
    return {
      totalLeads: 0,
      convertedLeads: 0,
      conversionRate: 0,
      averageConversionTime: 0,
      leadSources: {},
      generatedAt: new Date(),
    };
  }

  @Mock("ReportService-generateActivitySummary")
  async generateActivitySummary(): Promise<any> {
    // Placeholder implementation for activity summary
    return {
      totalActivities: 0,
      completedActivities: 0,
      pendingActivities: 0,
      overdueActivities: 0,
      activityTypes: {},
      generatedAt: new Date(),
    };
  }
}
