import { Provider } from '@core/decorators/decorators';
import { <PERSON><PERSON> } from '@core/decorators/mock.decorator';
import * as nodemailer from 'nodemailer';
import { Brand } from '../../entities/brand.entity';
import { ThreatDetection } from '../../entities/threat-detection.entity';
import { User } from '../../entities/user.entity';
import { UserService } from './user.service';
import { BrandService } from './brand.service';

interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

interface SMSConfig {
  apiKey: string;
  apiUrl: string;
  from: string;
}

@Provider()
export class EnhancedNotificationService {
  private transporter: nodemailer.Transporter;
  private smsConfig: SMSConfig;

  constructor(
    private userService: UserService,
    private brandService: BrandService
  ) {
    this.initializeEmailTransporter();
    this.initializeSMSConfig();
  }

  private initializeEmailTransporter() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  private initializeSMSConfig() {
    this.smsConfig = {
      apiKey: process.env.SMS_API_KEY || '',
      apiUrl: process.env.SMS_API_URL || 'https://api.twilio.com/2010-04-01',
      from: process.env.SMS_FROM || ''
    };
  }

  @Mock('EnhancedNotificationService-sendThreatAlert')
  async sendThreatAlert(threat: ThreatDetection): Promise<void> {
    try {
      // Get brand and owner information
      const brand = await this.brandService.findById(threat.brandId);
      if (!brand) {
        console.error(`Brand not found for threat ${threat.id}`);
        return;
      }

      const owner = await this.userService.findById(brand.ownerId);
      if (!owner) {
        console.error(`Brand owner not found for brand ${brand.id}`);
        return;
      }

      // Get notification preferences
      const recipients = await this.getNotificationRecipients(brand, threat.severity);

      // Send email notifications
      if (recipients.email.length > 0) {
        await this.sendThreatEmailAlert(recipients.email, threat, brand);
      }

      // Send SMS for critical threats
      if (threat.severity === 'critical' && recipients.sms.length > 0) {
        await this.sendThreatSMSAlert(recipients.sms, threat, brand);
      }

      console.log(`Threat alert sent for ${threat.id} to ${recipients.email.length} email(s) and ${recipients.sms.length} SMS(s)`);

    } catch (error) {
      console.error(`Error sending threat alert for ${threat.id}:`, error);
    }
  }

  @Mock('EnhancedNotificationService-sendEmail')
  async sendEmail(to: string | string[], subject: string, html: string, text?: string): Promise<void> {
    const recipients = Array.isArray(to) ? to.join(', ') : to;
    
    await this.transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: recipients,
      subject,
      html,
      text: text || this.htmlToText(html),
    });
  }

  @Mock('EnhancedNotificationService-sendSMS')
  async sendSMS(to: string, message: string): Promise<void> {
    if (!this.smsConfig.apiKey) {
      console.warn('SMS API not configured, skipping SMS notification');
      return;
    }

    try {
      // Implementation would depend on SMS provider (Twilio, AWS SNS, etc.)
      // This is a placeholder for the actual SMS sending logic
      console.log(`SMS would be sent to ${to}: ${message}`);
    } catch (error) {
      console.error(`Error sending SMS to ${to}:`, error);
    }
  }

  private async sendThreatEmailAlert(
    recipients: User[],
    threat: ThreatDetection,
    brand: Brand
  ): Promise<void> {
    const template = this.getThreatAlertTemplate(threat, brand);
    const emails = recipients.map(user => user.email).filter(email => email);
    
    if (emails.length > 0) {
      await this.sendEmail(emails, template.subject, template.html, template.text);
    }
  }

  private async sendThreatSMSAlert(
    recipients: User[],
    threat: ThreatDetection,
    brand: Brand
  ): Promise<void> {
    const message = `🚨 CRITICAL: ${threat.threatType} threat detected for ${brand.name}. Check ${threat.sourceUrl}. Login to Monitora for details.`;
    
    for (const user of recipients) {
      if (user.phone) {
        await this.sendSMS(user.phone, message);
      }
    }
  }

  private async getNotificationRecipients(brand: Brand, severity: string): Promise<{
    email: User[];
    sms: User[];
  }> {
    const recipients = { email: [], sms: [] };

    try {
      // Get brand owner
      const owner = await this.userService.findById(brand.ownerId);
      if (owner) {
        recipients.email.push(owner);
        if (severity === 'critical') {
          recipients.sms.push(owner);
        }
      }

      // Get users with brand access based on alert frequency settings
      const alertFrequency = brand.monitoringSettings?.alertFrequency || 'realtime';
      
      if (alertFrequency === 'realtime' || severity === 'critical') {
        // Add additional recipients based on brand permissions
        // This would integrate with your RBAC system
        const brandUsers = await this.getBrandUsers(brand.id);
        recipients.email.push(...brandUsers);
        
        if (severity === 'critical') {
          recipients.sms.push(...brandUsers.filter(user => user.phone));
        }
      }

    } catch (error) {
      console.error('Error getting notification recipients:', error);
    }

    return recipients;
  }

  private async getBrandUsers(brandId: string): Promise<User[]> {
    // This would integrate with your RBAC system to get users with brand access
    // For now, return empty array
    return [];
  }

  private getThreatAlertTemplate(threat: ThreatDetection, brand: Brand): EmailTemplate {
    const severityEmoji = {
      low: '🟡',
      medium: '🟠', 
      high: '🔴',
      critical: '🚨'
    };

    const subject = `${severityEmoji[threat.severity]} ${threat.severity.toUpperCase()} Threat Detected - ${brand.name}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
          .alert-critical { border-left: 5px solid #dc3545; }
          .alert-high { border-left: 5px solid #fd7e14; }
          .alert-medium { border-left: 5px solid #ffc107; }
          .alert-low { border-left: 5px solid #28a745; }
          .details { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="header alert-${threat.severity}">
          <h2>${severityEmoji[threat.severity]} Threat Detection Alert</h2>
          <p><strong>Brand:</strong> ${brand.name}</p>
        </div>
        
        <div class="details">
          <h3>Threat Details</h3>
          <p><strong>Type:</strong> ${threat.threatType}</p>
          <p><strong>Severity:</strong> ${threat.severity.toUpperCase()}</p>
          <p><strong>Confidence:</strong> ${Math.round(threat.confidenceScore * 100)}%</p>
          <p><strong>Source:</strong> <a href="${threat.sourceUrl}">${threat.sourceUrl}</a></p>
          <p><strong>Description:</strong> ${threat.description}</p>
          <p><strong>Detected:</strong> ${new Date(threat.createdAt).toLocaleString()}</p>
        </div>

        ${threat.evidence?.screenshots?.length ? `
        <div class="details">
          <h3>Evidence</h3>
          <p>Screenshots and evidence have been collected for this threat.</p>
        </div>
        ` : ''}

        <div style="margin: 20px 0;">
          <a href="${process.env.FRONTEND_URL}/threats/${threat.id}" class="button">View Threat Details</a>
          <a href="${process.env.FRONTEND_URL}/brands/${brand.id}" class="button">Manage Brand</a>
        </div>

        <p><small>This is an automated alert from Monitora Brand Protection System.</small></p>
      </body>
      </html>
    `;

    const text = `
      THREAT DETECTION ALERT
      
      Brand: ${brand.name}
      Type: ${threat.threatType}
      Severity: ${threat.severity.toUpperCase()}
      Confidence: ${Math.round(threat.confidenceScore * 100)}%
      Source: ${threat.sourceUrl}
      Description: ${threat.description}
      Detected: ${new Date(threat.createdAt).toLocaleString()}
      
      View details: ${process.env.FRONTEND_URL}/threats/${threat.id}
    `;

    return { subject, html, text };
  }

  private htmlToText(html: string): string {
    // Simple HTML to text conversion
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .trim();
  }

  @Mock('EnhancedNotificationService-testEmailConfiguration')
  async testEmailConfiguration(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error('Email configuration test failed:', error);
      return false;
    }
  }

  @Mock('EnhancedNotificationService-findById')
  async findById(id: string): Promise<User | null> {
    // This should be implemented to find user by ID
    // For now, return null as placeholder
    return null;
  }
}
