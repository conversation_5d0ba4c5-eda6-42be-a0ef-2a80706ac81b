import { Provider } from "@core/decorators/decorators";
import { UserService } from "./user.service";
import { TokenProvider } from "../providers/token.provider";
import { Mock } from "@core/decorators/mock.decorator";
import * as bcrypt from "bcrypt";

@Provider()
export class AuthService {
  constructor(
    private userService: UserService,
    private tokenProvider: TokenProvider
  ) {}

  @Mock("AuthService-login")
  async login(username: string, password: string) {
    const result = await this.userService.getUser(username);
    if (!result.success || !result.user) {
      throw new Error("User not found");
    }

    // Get the full user entity for password comparison
    const fullUser = await this.userService.getUserWithPassword(username);
    if (!fullUser) {
      throw new Error("User not found");
    }

    const isValid = await bcrypt.compare(password, fullUser.password);
    if (!isValid) {
      throw new Error("Invalid password");
    }

    const token = this.tokenProvider.getToken(result.user);
    return { user: result.user, token };
  }

  @Mock("AuthService-verifyToken")
  verifyToken(token: string) {
    return this.tokenProvider.verifyToken(token);
  }
}
