import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Middleware,
} from "@core/decorators/decorators";
import { Request, Response } from "express";
import { ThreatDetectionService } from "../services/threat-detection.service";
import { BrandService } from "../services/brand.service";
import { auth } from "../middlewares/auth.middleware";

@Controller("/threats")
export class ThreatController {
  constructor(
    private threatDetectionService: ThreatDetectionService,
    private brandService: BrandService
  ) {}

  @Get("/brand/:brandId")
  @Middleware(auth)
  async getThreatsForBrand(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const { status, severity, threatType, sourceType } = req.query;
      const filters: any = {};

      if (status) filters.status = status;
      if (severity) filters.severity = severity;
      if (threatType) filters.threatType = threatType;
      if (sourceType) filters.sourceType = sourceType;

      const threats = await this.threatDetectionService.getThreatsForBrand(
        req.params.brandId,
        filters
      );

      res.json(threats);
    } catch (error) {
      res.status(500).json({
        message: "Error fetching threats",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Get("/:id")
  @Middleware(auth)
  async getThreat(req: Request, res: Response) {
    try {
      const threat = await this.threatDetectionService.findById(req.params.id);
      if (!threat) {
        return res.status(404).json({ message: "Threat not found" });
      }

      // Check brand ownership
      const brand = await this.brandService.findById(threat.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      res.json(threat);
    } catch (error) {
      res.status(500).json({
        message: "Error fetching threat",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Put("/:id/status")
  @Middleware(auth)
  async updateStatus(req: Request, res: Response) {
    try {
      const { status, notes } = req.body;

      if (!status) {
        return res.status(400).json({ message: "Status is required" });
      }

      const threat = await this.threatDetectionService.findById(req.params.id);
      if (!threat) {
        return res.status(404).json({ message: "Threat not found" });
      }

      // Check brand ownership
      const brand = await this.brandService.findById(threat.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedThreat = await this.threatDetectionService.updateStatus(
        req.params.id,
        status,
        notes
      );

      res.json(updatedThreat);
    } catch (error) {
      res.status(500).json({
        message: "Error updating threat status",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Post("/:id/mitigation")
  @Middleware(auth)
  async addMitigationStep(req: Request, res: Response) {
    try {
      const { step, assignedTo, dueDate, notes } = req.body;

      if (!step || !assignedTo || !dueDate) {
        return res.status(400).json({
          message: "Step, assignedTo, and dueDate are required",
        });
      }

      const threat = await this.threatDetectionService.findById(req.params.id);
      if (!threat) {
        return res.status(404).json({ message: "Threat not found" });
      }

      // Check brand ownership
      const brand = await this.brandService.findById(threat.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedThreat = await this.threatDetectionService.addMitigationStep(
        req.params.id,
        { step, assignedTo, dueDate: new Date(dueDate), notes }
      );

      res.json(updatedThreat);
    } catch (error) {
      res.status(500).json({
        message: "Error adding mitigation step",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Put("/:id/mitigation/:stepIndex")
  @Middleware(auth)
  async updateMitigationStep(req: Request, res: Response) {
    try {
      const stepIndex = parseInt(req.params.stepIndex);
      const { status, notes } = req.body;

      if (isNaN(stepIndex)) {
        return res.status(400).json({ message: "Invalid step index" });
      }

      const threat = await this.threatDetectionService.findById(req.params.id);
      if (!threat) {
        return res.status(404).json({ message: "Threat not found" });
      }

      // Check brand ownership
      const brand = await this.brandService.findById(threat.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedThreat =
        await this.threatDetectionService.updateMitigationStep(
          req.params.id,
          stepIndex,
          { status, notes }
        );

      res.json(updatedThreat);
    } catch (error) {
      res.status(500).json({
        message: "Error updating mitigation step",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Get("/stats/overview")
  @Middleware(auth)
  async getOverviewStats(req: Request, res: Response) {
    try {
      const stats = await this.threatDetectionService.getThreatStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({
        message: "Error fetching threat stats",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Get("/stats/brand/:brandId")
  @Middleware(auth)
  async getBrandStats(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const stats = await this.threatDetectionService.getThreatStats(
        req.params.brandId
      );
      res.json(stats);
    } catch (error) {
      res.status(500).json({
        message: "Error fetching brand threat stats",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Get("/high-priority")
  @Middleware(auth)
  async getHighPriorityThreats(req: Request, res: Response) {
    try {
      const threats =
        await this.threatDetectionService.getHighPriorityThreats();

      // Filter by user's brands
      const userBrands = await this.brandService.findByOwner(
        req.currentUser.id
      );
      const userBrandIds = userBrands.map((brand) => brand.id);

      const filteredThreats = threats.filter((threat) =>
        userBrandIds.includes(threat.brandId)
      );

      res.json(filteredThreats);
    } catch (error) {
      res.status(500).json({
        message: "Error fetching high priority threats",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  @Post("/bulk-update")
  @Middleware(auth)
  async bulkUpdateStatus(req: Request, res: Response) {
    try {
      const { threatIds, status } = req.body;

      if (!threatIds || !Array.isArray(threatIds) || !status) {
        return res.status(400).json({
          message: "threatIds (array) and status are required",
        });
      }

      // Verify ownership of all threats
      for (const threatId of threatIds) {
        const threat = await this.threatDetectionService.findById(threatId);
        if (!threat) {
          return res
            .status(404)
            .json({ message: `Threat ${threatId} not found` });
        }

        const brand = await this.brandService.findById(threat.brandId);
        if (!brand || brand.ownerId !== req.currentUser.id) {
          return res.status(403).json({
            message: `Access denied for threat ${threatId}`,
          });
        }
      }

      await this.threatDetectionService.bulkUpdateStatus(threatIds, status);
      res.json({ message: "Threats updated successfully" });
    } catch (error) {
      res
        .status(500)
        .json({
          message: "Error bulk updating threats",
          error: error instanceof Error ? error.message : "Unknown error",
        });
    }
  }
}
