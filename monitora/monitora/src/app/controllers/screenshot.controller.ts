import { Request, Response } from 'express';
import { Provider } from '@core/decorators/decorators';
import { ScreenshotService } from '../services/screenshot.service';
import * as path from 'path';
import * as fs from 'fs';

@Provider()
export class ScreenshotController {
  constructor(private screenshotService: ScreenshotService) {}

  async captureScreenshot(req: Request, res: Response): Promise<void> {
    try {
      const { url } = req.body;
      const { 
        width = 1920, 
        height = 1080, 
        fullPage = true, 
        quality = 90 
      } = req.body.options || {};

      if (!url) {
        res.status(400).json({ error: 'URL is required' });
        return;
      }

      // Validate URL format
      try {
        new URL(url);
      } catch {
        res.status(400).json({ error: 'Invalid URL format' });
        return;
      }

      const screenshot = await this.screenshotService.captureWebsite(url, {
        width: parseInt(width),
        height: parseInt(height),
        fullPage: fullPage === true || fullPage === 'true',
        quality: parseInt(quality)
      });

      res.json({
        success: true,
        screenshot: {
          ...screenshot,
          url: this.screenshotService.getScreenshotUrl(screenshot.filename)
        }
      });

    } catch (error) {
      console.error('Error capturing screenshot:', error);
      res.status(500).json({ 
        error: 'Failed to capture screenshot',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async captureMultiple(req: Request, res: Response): Promise<void> {
    try {
      const { urls, options = {} } = req.body;

      if (!urls || !Array.isArray(urls) || urls.length === 0) {
        res.status(400).json({ error: 'URLs array is required' });
        return;
      }

      if (urls.length > 10) {
        res.status(400).json({ error: 'Maximum 10 URLs allowed per batch' });
        return;
      }

      // Validate all URLs
      for (const url of urls) {
        try {
          new URL(url);
        } catch {
          res.status(400).json({ error: `Invalid URL format: ${url}` });
          return;
        }
      }

      const results = await this.screenshotService.captureMultiple(urls, options);

      res.json({
        success: true,
        results: results.map(result => ({
          ...result,
          screenshot: result.screenshot ? {
            ...result.screenshot,
            url: this.screenshotService.getScreenshotUrl(result.screenshot.filename)
          } : undefined
        }))
      });

    } catch (error) {
      console.error('Error capturing multiple screenshots:', error);
      res.status(500).json({ 
        error: 'Failed to capture screenshots',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async captureWithMetadata(req: Request, res: Response): Promise<void> {
    try {
      const { url, metadata = {} } = req.body;

      if (!url) {
        res.status(400).json({ error: 'URL is required' });
        return;
      }

      try {
        new URL(url);
      } catch {
        res.status(400).json({ error: 'Invalid URL format' });
        return;
      }

      const result = await this.screenshotService.captureWithMetadata(url, metadata);

      res.json({
        success: true,
        result: {
          ...result,
          screenshot: {
            ...result.screenshot,
            url: this.screenshotService.getScreenshotUrl(result.screenshot.filename)
          }
        }
      });

    } catch (error) {
      console.error('Error capturing screenshot with metadata:', error);
      res.status(500).json({ 
        error: 'Failed to capture screenshot with metadata',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getScreenshot(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params;

      // Validate filename to prevent directory traversal
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const screenshotPath = this.screenshotService.getScreenshotPath(filename);

      // Check if file exists
      if (!fs.existsSync(screenshotPath)) {
        res.status(404).json({ error: 'Screenshot not found' });
        return;
      }

      // Get file stats
      const stats = fs.statSync(screenshotPath);

      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
      
      const stream = fs.createReadStream(screenshotPath);
      stream.pipe(res);

    } catch (error) {
      console.error('Error serving screenshot:', error);
      res.status(500).json({ 
        error: 'Failed to serve screenshot',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async deleteScreenshot(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params;

      // Validate filename
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const deleted = await this.screenshotService.deleteScreenshot(filename);

      if (deleted) {
        res.json({ success: true, message: 'Screenshot deleted successfully' });
      } else {
        res.status(404).json({ error: 'Screenshot not found' });
      }

    } catch (error) {
      console.error('Error deleting screenshot:', error);
      res.status(500).json({ 
        error: 'Failed to delete screenshot',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async cleanupOldScreenshots(req: Request, res: Response): Promise<void> {
    try {
      // TODO: Add authorization check - user should be admin
      
      const { daysOld = 30 } = req.query;
      const days = parseInt(daysOld as string);

      if (isNaN(days) || days < 1) {
        res.status(400).json({ error: 'Invalid daysOld parameter' });
        return;
      }

      const deletedCount = await this.screenshotService.cleanupOldScreenshots(days);

      res.json({
        success: true,
        message: `Cleaned up ${deletedCount} old screenshots`,
        deletedCount
      });

    } catch (error) {
      console.error('Error cleaning up screenshots:', error);
      res.status(500).json({ 
        error: 'Failed to cleanup screenshots',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getScreenshotInfo(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params;

      // Validate filename
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        res.status(400).json({ error: 'Invalid filename' });
        return;
      }

      const screenshotPath = this.screenshotService.getScreenshotPath(filename);

      if (!fs.existsSync(screenshotPath)) {
        res.status(404).json({ error: 'Screenshot not found' });
        return;
      }

      const stats = fs.statSync(screenshotPath);

      res.json({
        success: true,
        info: {
          filename,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          url: this.screenshotService.getScreenshotUrl(filename)
        }
      });

    } catch (error) {
      console.error('Error getting screenshot info:', error);
      res.status(500).json({ 
        error: 'Failed to get screenshot info',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
