import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { User } from "./user.entity";

@Entity("leads")
export class Lead {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  company: string;

  @Column()
  source: string;

  @Column({
    type: "enum",
    enum: ["new", "contacted", "qualified", "unqualified"],
  })
  status: "new" | "contacted" | "qualified" | "unqualified";

  @Column({ type: "int", default: 0 })
  score: number;

  @ManyToOne(() => User, (user) => user.leads)
  assignedTo: User;

  @Column({ nullable: true })
  assignedToId: string;

  @Column({ type: "text", nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
