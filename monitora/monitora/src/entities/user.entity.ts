import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToMany,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Opportunity } from "./opportunity.entity";
import { Contact } from "./contact.entity";
import { Task } from "./task.entity";
import { Brand } from "./brand.entity";
import { Alert } from "./alert.entity";
import { TakedownRequest } from "./takedown-request.entity";
import { MonitoringRule } from "./monitoring-rule.entity";
import { Role } from "./role.entity";
import { Lead } from "./lead.entity";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  username: string;

  @Column()
  email: string;

  @Column()
  password: string;

  @Column()
  name: string;

  @Column({
    default: "active",
    type: "enum",
    enum: ["active", "inactive", "suspended"],
  })
  status: "active" | "inactive" | "suspended";

  @Column({
    type: "enum",
    enum: ["system_admin", "admin", "editor", "viewer"],
    default: "viewer",
  })
  defaultRole: "system_admin" | "admin" | "editor" | "viewer";

  @OneToMany(() => Task, (task: Task) => task.assignedTo)
  tasks: Task[];

  @OneToMany(() => Opportunity, (opportunity) => opportunity.owner)
  opportunities: Opportunity[];

  @OneToMany(() => Contact, (contact) => contact.assignedTo)
  contacts: Contact[];

  @OneToMany(() => Lead, (lead) => lead.assignedTo)
  leads: Lead[];

  @OneToMany(() => Brand, (brand) => brand.owner)
  brands: Brand[];

  @OneToMany(() => Alert, (alert) => alert.recipient)
  alerts: Alert[];

  @OneToMany(
    () => TakedownRequest,
    (takedownRequest) => takedownRequest.assignedTo
  )
  takedownRequests: TakedownRequest[];

  @OneToMany(() => MonitoringRule, (monitoringRule) => monitoringRule.createdBy)
  monitoringRules: MonitoringRule[];

  @ManyToMany(() => Role, (role) => role.users)
  @JoinTable({
    name: "user_roles",
    joinColumn: { name: "user_id", referencedColumnName: "id" },
    inverseJoinColumn: { name: "role_id", referencedColumnName: "id" },
  })
  roles: Role[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
