version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: monitora-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: monitora_db
      POSTGRES_USER: monitora_user
      POSTGRES_PASSWORD: monitora_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - monitora-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U monitora_user -d monitora_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: monitora-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass monitora_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - monitora-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: monitora-app
    restart: unless-stopped
    environment:
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: monitora_user
      DB_PASSWORD: monitora_password
      DB_DATABASE: monitora_db
      
      # Application
      NODE_ENV: production
      PORT: 3000
      JWT_SECRET: your_super_secret_jwt_key_change_this_in_production
      FRONTEND_URL: http://localhost:3000
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: monitora_redis_password
      
      # Email Configuration
      SMTP_HOST: ${SMTP_HOST:-smtp.gmail.com}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_SECURE: ${SMTP_SECURE:-false}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      SMTP_FROM: ${SMTP_FROM:-<EMAIL>}
      
      # SMS Configuration
      SMS_API_KEY: ${SMS_API_KEY}
      SMS_API_URL: ${SMS_API_URL:-https://api.twilio.com/2010-04-01}
      SMS_FROM: ${SMS_FROM}
      
      # API Keys
      VIRUSTOTAL_API_KEY: ${VIRUSTOTAL_API_KEY}
      META_ACCESS_TOKEN: ${META_ACCESS_TOKEN}
      META_APP_ID: ${META_APP_ID}
      META_APP_SECRET: ${META_APP_SECRET}
      TWITTER_BEARER_TOKEN: ${TWITTER_BEARER_TOKEN}
      TWITTER_API_KEY: ${TWITTER_API_KEY}
      TWITTER_API_SECRET: ${TWITTER_API_SECRET}
      GOOGLE_SEARCH_API_KEY: ${GOOGLE_SEARCH_API_KEY}
      GOOGLE_SEARCH_ENGINE_ID: ${GOOGLE_SEARCH_ENGINE_ID}
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
      
      # Screenshot Service
      SCREENSHOT_STORAGE_PATH: /usr/src/app/screenshots
      
      # Monitoring
      MONITORING_INTERVAL_HOURS: 1
      THREAT_ANALYSIS_INTERVAL_MINUTES: 30
      CLEANUP_INTERVAL_HOURS: 24
      
      # Security
      BCRYPT_ROUNDS: 12
      SESSION_SECRET: your_session_secret_change_this
      CORS_ORIGIN: http://localhost:3000
      
      # Features
      ENABLE_REAL_TIME_MONITORING: true
      ENABLE_AUTOMATED_RESPONSES: false
      ENABLE_ML_THREAT_DETECTION: false
      ENABLE_ADVANCED_ANALYTICS: true
      
      # Puppeteer
      PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: true
      PUPPETEER_EXECUTABLE_PATH: /usr/bin/google-chrome-stable
    
    volumes:
      - app_screenshots:/usr/src/app/screenshots
      - app_reports:/usr/src/app/reports
      - app_logs:/usr/src/app/logs
      - app_uploads:/usr/src/app/uploads
      - app_backups:/usr/src/app/backups
    
    ports:
      - "3000:3000"
    
    networks:
      - monitora-network
    
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: monitora-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - app_screenshots:/var/www/screenshots:ro
      - app_reports:/var/www/reports:ro
    networks:
      - monitora-network
    depends_on:
      - app

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: monitora-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - monitora-network
    profiles:
      - monitoring

  # Grafana for Dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: monitora-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - monitora-network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_screenshots:
    driver: local
  app_reports:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  app_backups:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  monitora-network:
    driver: bridge
