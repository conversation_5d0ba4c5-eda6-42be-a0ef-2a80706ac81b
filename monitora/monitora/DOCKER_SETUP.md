# 🐳 Docker Setup for Monitora Brand Protection

## 📋 **Prerequisites**

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers
- 10GB free disk space

## 🔧 **Docker Installation**

### **macOS**
```bash
# Option 1: Docker Desktop (Recommended)
# Download from: https://www.docker.com/products/docker-desktop/
# Or install via Homebrew:
brew install --cask docker

# Option 2: Homebrew (CLI only)
brew install docker docker-compose
```

### **Ubuntu/Debian**
```bash
# Update package index
sudo apt-get update

# Install dependencies
sudo apt-get install ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

### **CentOS/RHEL/Fedora**
```bash
# Install Docker
sudo dnf install docker docker-compose

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

### **Windows**
```bash
# Download Docker Desktop from:
# https://www.docker.com/products/docker-desktop/

# Or use Windows Subsystem for Linux (WSL2) with Ubuntu instructions above
```

### **Verify Installation**
```bash
# Check Docker version
docker --version

# Check Docker Compose version (modern syntax)
docker compose version

# Test Docker installation
docker run hello-world
```

## 🚀 **Quick Start**

### **Production Deployment**

1. **Clone and configure environment:**
```bash
git clone <repository>
cd monitora/monitora

# Copy and configure environment
cp .env.docker .env
# Edit .env with your API keys and configuration
```

2. **Start all services:**
```bash
docker compose up -d
```

3. **Access the application:**
- **API**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/health
- **Nginx Proxy**: http://localhost (if enabled)

### **Development Setup**

1. **Start development environment:**
```bash
npm run docker:dev
```

2. **Access development tools:**
- **Application**: http://localhost:3000
- **pgAdmin**: http://localhost:5050 (<EMAIL> / admin)
- **Redis Commander**: http://localhost:8081
- **MailHog**: http://localhost:8025 (email testing)

## 🏗️ **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │   Monitora App  │    │   PostgreSQL    │
│  (Reverse Proxy)│◄──►│   (Node.js)     │◄──►│   (Database)    │
│     Port 80     │    │    Port 3000    │    │    Port 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │   (Caching)     │
                       │    Port 6379    │
                       └─────────────────┘
```

## 📦 **Services Included**

### **Core Services**
- **app**: Main Monitora application (Node.js + TypeScript)
- **postgres**: PostgreSQL 15 database
- **redis**: Redis 7 for caching and sessions
- **nginx**: Reverse proxy and static file server

### **Development Services** (dev compose only)
- **mailhog**: Email testing server
- **pgadmin**: PostgreSQL administration
- **redis-commander**: Redis management interface

### **Optional Services** (monitoring profile)
- **prometheus**: Metrics collection
- **grafana**: Monitoring dashboards

## 🔧 **Configuration**

### **Environment Variables**

Copy `.env.docker` to `.env` and configure:

```bash
# Required for email alerts
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Recommended for malware scanning
VIRUSTOTAL_API_KEY=your_virustotal_api_key

# Optional API integrations
META_ACCESS_TOKEN=your_meta_token
TWITTER_BEARER_TOKEN=your_twitter_token
GOOGLE_SEARCH_API_KEY=your_google_key
```

### **Volume Mounts**

Persistent data is stored in Docker volumes:
- `postgres_data`: Database files
- `redis_data`: Redis persistence
- `app_screenshots`: Screenshot storage
- `app_reports`: Generated reports
- `app_logs`: Application logs

## 🛠️ **Docker Commands**

### **Production Commands**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up --build -d

# Scale the application
docker-compose up -d --scale app=3
```

### **Development Commands**
```bash
# Start development environment
npm run docker:dev

# View development logs
docker-compose -f docker-compose.dev.yml logs -f app-dev

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Rebuild development containers
docker-compose -f docker-compose.dev.yml up --build
```

### **Monitoring Commands**
```bash
# Start with monitoring stack
docker-compose --profile monitoring up -d

# Access Grafana
open http://localhost:3001

# Access Prometheus
open http://localhost:9090
```

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Port conflicts:**
```bash
# Check what's using the ports
lsof -i :3000
lsof -i :5432

# Change ports in docker-compose.yml if needed
```

2. **Permission issues with screenshots:**
```bash
# Fix volume permissions
docker-compose exec app chown -R monitora:monitora /usr/src/app/screenshots
```

3. **Database connection issues:**
```bash
# Check database logs
docker-compose logs postgres

# Reset database
docker-compose down
docker volume rm monitora_postgres_data
docker-compose up -d
```

4. **Puppeteer/Chrome issues:**
```bash
# Check if Chrome is working
docker-compose exec app google-chrome-stable --version

# View app logs for Puppeteer errors
docker-compose logs app | grep -i puppeteer
```

### **Health Checks**

```bash
# Check all service health
docker-compose ps

# Test application health
curl http://localhost:3000/health

# Test database connection
docker-compose exec postgres pg_isready -U monitora_user

# Test Redis connection
docker-compose exec redis redis-cli ping
```

## 📊 **Monitoring & Logs**

### **Application Logs**
```bash
# Follow application logs
docker-compose logs -f app

# View specific service logs
docker-compose logs nginx
docker-compose logs postgres
docker-compose logs redis
```

### **Performance Monitoring**
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Clean up unused resources
docker system prune -a
```

## 🔒 **Security Considerations**

### **Production Security**
1. **Change default passwords** in docker-compose.yml
2. **Use environment files** for sensitive data
3. **Enable HTTPS** in nginx configuration
4. **Restrict network access** using Docker networks
5. **Regular security updates** of base images

### **SSL/HTTPS Setup**
1. Obtain SSL certificates
2. Place certificates in `docker/nginx/ssl/`
3. Uncomment HTTPS server block in nginx.conf
4. Update environment variables for HTTPS URLs

## 🚀 **Deployment Options**

### **Single Server Deployment**
```bash
# Production deployment
docker-compose up -d

# With monitoring
docker-compose --profile monitoring up -d
```

### **Docker Swarm Deployment**
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml monitora
```

### **Kubernetes Deployment**
Convert docker-compose.yml to Kubernetes manifests:
```bash
# Using kompose
kompose convert -f docker-compose.yml
kubectl apply -f .
```

## 📈 **Scaling**

### **Horizontal Scaling**
```bash
# Scale application containers
docker-compose up -d --scale app=3

# Use load balancer (nginx already configured)
```

### **Resource Limits**
Add to docker-compose.yml:
```yaml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
```

## 🔄 **Backup & Recovery**

### **Database Backup**
```bash
# Create backup
docker-compose exec postgres pg_dump -U monitora_user monitora_db > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U monitora_user monitora_db < backup.sql
```

### **Volume Backup**
```bash
# Backup volumes
docker run --rm -v monitora_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v monitora_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data
```

## 📞 **Support**

For issues with Docker setup:
1. Check the troubleshooting section above
2. Review Docker and application logs
3. Ensure all prerequisites are met
4. Verify environment configuration
