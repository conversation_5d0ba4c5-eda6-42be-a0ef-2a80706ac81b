-- PostgreSQL initialization script for Monitora
-- This script runs when the PostgreSQL container starts for the first time

-- Create additional databases if needed
-- CREATE DATABASE monitora_test;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE monitora_db TO monitora_user;

-- Create indexes for better performance (will be created by TypeORM, but can be pre-created)
-- These will be created automatically by TypeORM migrations
